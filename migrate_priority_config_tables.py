#!/usr/bin/env python3
"""
优先级配置表数据库迁移脚本
重建表结构以匹配Excel文件的完整字段
"""

import mysql.connector
import sys
import os
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': 'WWWwww123!',
    'database': 'aps_system',
    'charset': 'utf8mb4'
}

def connect_db():
    """连接数据库"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def backup_existing_tables(conn):
    """备份现有表"""
    try:
        cursor = conn.cursor()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 备份devicepriorityconfig表
        try:
            cursor.execute("SHOW TABLES LIKE 'devicepriorityconfig'")
            if cursor.fetchone():
                backup_table_name = f'devicepriorityconfig_backup_{timestamp}'
                cursor.execute(f"CREATE TABLE {backup_table_name} AS SELECT * FROM devicepriorityconfig")
                print(f"✅ 备份表: devicepriorityconfig -> {backup_table_name}")
        except Exception as e:
            print(f"⚠️  devicepriorityconfig表备份失败: {e}")
        
        # 备份lotpriorityconfig表
        try:
            cursor.execute("SHOW TABLES LIKE 'lotpriorityconfig'")
            if cursor.fetchone():
                backup_table_name = f'lotpriorityconfig_backup_{timestamp}'
                cursor.execute(f"CREATE TABLE {backup_table_name} AS SELECT * FROM lotpriorityconfig")
                print(f"✅ 备份表: lotpriorityconfig -> {backup_table_name}")
        except Exception as e:
            print(f"⚠️  lotpriorityconfig表备份失败: {e}")
        
        conn.commit()
        cursor.close()
        
    except Exception as e:
        print(f"❌ 备份表失败: {e}")

def drop_existing_tables(conn):
    """删除现有表"""
    try:
        cursor = conn.cursor()
        
        # 删除现有表
        tables_to_drop = ['devicepriorityconfig', 'lotpriorityconfig']
        
        for table in tables_to_drop:
            try:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                print(f"🗑️  删除表: {table}")
            except Exception as e:
                print(f"⚠️  删除表{table}失败: {e}")
        
        conn.commit()
        cursor.close()
        
    except Exception as e:
        print(f"❌ 删除表失败: {e}")

def create_device_priority_config_table(conn):
    """创建完整的devicepriorityconfig表"""
    try:
        cursor = conn.cursor()
        
        create_sql = """
        CREATE TABLE devicepriorityconfig (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            device VARCHAR(200) NOT NULL COMMENT 'device - 产品名称',
            stage VARCHAR(100) DEFAULT NULL COMMENT 'stage - 工序',
            handler_config VARCHAR(100) DEFAULT NULL COMMENT 'handler_config - 分选机配置',
            handler_priority VARCHAR(10) DEFAULT NULL COMMENT 'handler_priority - 分选机优先级',
            setup_qty INT DEFAULT NULL COMMENT 'setup_qty - 设置数量',
            priority VARCHAR(10) COMMENT 'priority - 优先级',
            price DECIMAL(10,2) DEFAULT NULL COMMENT 'price - 价格',
            from_time DATETIME DEFAULT NULL COMMENT 'from_time - 开始时间',
            end_time DATETIME DEFAULT NULL COMMENT 'end_time - 结束时间',
            refresh_time DATETIME DEFAULT NULL COMMENT 'refresh_time - 刷新时间',
            user VARCHAR(100) DEFAULT NULL COMMENT 'user - 用户',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_device (device),
            INDEX idx_stage (stage),
            INDEX idx_priority (priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品优先级配置表(完整字段)'
        """
        
        cursor.execute(create_sql)
        print(f"✅ 创建表: devicepriorityconfig (完整字段)")
        
        conn.commit()
        cursor.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 创建devicepriorityconfig表失败: {e}")
        return False

def create_lot_priority_config_table(conn):
    """创建lotpriorityconfig表"""
    try:
        cursor = conn.cursor()
        
        create_sql = """
        CREATE TABLE lotpriorityconfig (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
            device VARCHAR(200) NOT NULL COMMENT 'device - 产品名称',
            stage VARCHAR(100) DEFAULT NULL COMMENT 'stage - 工序',
            priority VARCHAR(10) COMMENT 'priority - 优先级',
            refresh_time DATETIME DEFAULT NULL COMMENT 'refresh_time - 刷新时间',
            user VARCHAR(100) DEFAULT NULL COMMENT 'user - 用户',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_device (device),
            INDEX idx_stage (stage),
            INDEX idx_priority (priority)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批次优先级配置表(小写字段)'
        """
        
        cursor.execute(create_sql)
        print(f"✅ 创建表: lotpriorityconfig (小写字段)")
        
        conn.commit()
        cursor.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 创建lotpriorityconfig表失败: {e}")
        return False

def verify_tables(conn):
    """验证表结构"""
    try:
        cursor = conn.cursor()
        
        # 验证devicepriorityconfig表
        cursor.execute("DESCRIBE devicepriorityconfig")
        device_columns = cursor.fetchall()
        print("📋 devicepriorityconfig表结构:")
        for col in device_columns:
            print(f"  {col[0]} - {col[1]} - {col[4] or 'No comment'}")
        
        print()
        
        # 验证lotpriorityconfig表
        cursor.execute("DESCRIBE lotpriorityconfig")
        lot_columns = cursor.fetchall()
        print("📋 lotpriorityconfig表结构:")
        for col in lot_columns:
            print(f"  {col[0]} - {col[1]} - {col[4] or 'No comment'}")
        
        cursor.close()
        
    except Exception as e:
        print(f"❌ 验证表结构失败: {e}")

def main():
    """主函数"""
    print("🔧 开始迁移优先级配置表...")
    print("=" * 60)
    
    # 连接数据库
    conn = connect_db()
    if not conn:
        print("❌ 数据库连接失败，退出")
        sys.exit(1)
    
    try:
        # 1. 备份现有表
        print("1️⃣ 备份现有表...")
        backup_existing_tables(conn)
        print()
        
        # 2. 删除现有表
        print("2️⃣ 删除现有表...")
        drop_existing_tables(conn)
        print()
        
        # 3. 创建新的devicepriorityconfig表
        print("3️⃣ 创建新的devicepriorityconfig表...")
        device_success = create_device_priority_config_table(conn)
        print()
        
        # 4. 创建新的lotpriorityconfig表
        print("4️⃣ 创建新的lotpriorityconfig表...")
        lot_success = create_lot_priority_config_table(conn)
        print()
        
        # 5. 验证表结构
        if device_success and lot_success:
            print("5️⃣ 验证表结构...")
            verify_tables(conn)
            print()
            
            print("🎉 数据库迁移完成！")
            print("💡 接下来可以:")
            print("   1. 测试Excel文件导入功能")
            print("   2. 验证前端页面显示")
            print("   3. 检查所有CRUD操作")
        else:
            print("❌ 表创建失败，请检查错误信息")
    
    finally:
        conn.close()

if __name__ == "__main__":
    main() 