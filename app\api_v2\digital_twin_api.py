#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数字孪生API接口
提供设备数字孪生状态查询和管理功能
"""

import logging
from flask import Blueprint, request, jsonify
from app.services.digital_twin_service import DigitalTwinService
from app.services.data_collection_service import DataCollectionService
from flask_login import login_required

logger = logging.getLogger(__name__)

# 创建蓝图
digital_twin_api = Blueprint('digital_twin_api', __name__)

# 服务实例
digital_twin_service = DigitalTwinService()
data_collection_service = DataCollectionService()

@digital_twin_api.route('/api/v2/equipment/digital-twin/status', methods=['GET'])
def get_all_equipment_status():
    """获取所有设备的数字孪生状态"""
    try:
        from app.utils.db_helper import get_mysql_connection
        
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT 
                HANDLER_ID,
                current_status,
                DEVICE,
                STAGE,
                health_score,
                current_efficiency,
                estimated_uph,
                temperature,
                maintenance_urgency,
                last_update,
                overall_condition
            FROM v_equipment_real_time_status
            ORDER BY health_score DESC
        """)
        
        equipment_status = cursor.fetchall()
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'data': equipment_status,
            'total': len(equipment_status),
            'message': f'获取到 {len(equipment_status)} 台设备的数字孪生状态'
        })
        
    except Exception as e:
        logger.error(f"❌ 获取设备数字孪生状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取设备状态失败: {str(e)}',
            'data': []
        }), 500

@digital_twin_api.route('/api/v2/equipment/digital-twin/status/<equipment_id>', methods=['GET'])
def get_equipment_status(equipment_id):
    """获取指定设备的数字孪生状态"""
    try:
        status = digital_twin_service.get_equipment_twin_status(equipment_id)
        
        if status:
            return jsonify({
                'success': True,
                'data': status,
                'message': f'设备 {equipment_id} 状态获取成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'设备 {equipment_id} 未找到或未初始化数字孪生',
                'data': None
            }), 404
            
    except Exception as e:
        logger.error(f"❌ 获取设备 {equipment_id} 状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取设备状态失败: {str(e)}',
            'data': None
        }), 500

@digital_twin_api.route('/api/v2/equipment/digital-twin/initialize', methods=['POST'])
@login_required
def initialize_equipment_twin():
    """初始化设备数字孪生"""
    try:
        data = request.get_json()
        equipment_id = data.get('equipment_id')
        
        if not equipment_id:
            return jsonify({
                'success': False,
                'message': '缺少设备ID参数'
            }), 400
        
        success = digital_twin_service.initialize_equipment_twin(equipment_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'设备 {equipment_id} 数字孪生初始化成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'设备 {equipment_id} 数字孪生初始化失败'
            }), 500
            
    except Exception as e:
        logger.error(f"❌ 初始化设备数字孪生失败: {e}")
        return jsonify({
            'success': False,
            'message': f'初始化失败: {str(e)}'
        }), 500

@digital_twin_api.route('/api/v2/equipment/digital-twin/batch-initialize', methods=['POST'])
@login_required
def batch_initialize_twins():
    """批量初始化设备数字孪生"""
    try:
        success_count = digital_twin_service.batch_initialize_twins()
        
        return jsonify({
            'success': True,
            'message': f'批量初始化完成，成功初始化 {success_count} 台设备',
            'initialized_count': success_count
        })
        
    except Exception as e:
        logger.error(f"❌ 批量初始化设备数字孪生失败: {e}")
        return jsonify({
            'success': False,
            'message': f'批量初始化失败: {str(e)}',
            'initialized_count': 0
        }), 500

@digital_twin_api.route('/api/v2/equipment/digital-twin/update', methods=['POST'])
@login_required
def update_equipment_state():
    """更新设备状态（模拟传感器数据输入）"""
    try:
        data = request.get_json()
        equipment_id = data.get('equipment_id')
        sensor_data = data.get('sensor_data', {})
        
        if not equipment_id:
            return jsonify({
                'success': False,
                'message': '缺少设备ID参数'
            }), 400
        
        # 如果没有提供传感器数据，使用模拟数据
        if not sensor_data:
            sensor_data = digital_twin_service.simulate_sensor_data(equipment_id)
        
        state = digital_twin_service.update_equipment_state(equipment_id, sensor_data)
        
        return jsonify({
            'success': True,
            'message': f'设备 {equipment_id} 状态更新成功',
            'data': {
                'equipment_id': state.equipment_id,
                'health_score': state.health_score,
                'current_efficiency': state.current_efficiency,
                'estimated_uph': state.estimated_uph,
                'temperature': state.temperature,
                'timestamp': state.timestamp.isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"❌ 更新设备状态失败: {e}")
        return jsonify({
            'success': False,
            'message': f'更新设备状态失败: {str(e)}'
        }), 500

@digital_twin_api.route('/api/v2/equipment/switch-history', methods=['GET'])
@login_required
def get_switch_history():
    """获取设备切换历史"""
    try:
        equipment_id = request.args.get('equipment_id')
        limit = int(request.args.get('limit', 50))
        
        from app.utils.db_helper import get_mysql_connection
        
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        if equipment_id:
            cursor.execute("""
                SELECT * FROM equipment_switch_history 
                WHERE equipment_id = %s
                ORDER BY switch_timestamp DESC
                LIMIT %s
            """, (equipment_id, limit))
        else:
            cursor.execute("""
                SELECT * FROM equipment_switch_history 
                ORDER BY switch_timestamp DESC
                LIMIT %s
            """, (limit,))
        
        history = cursor.fetchall()
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'data': history,
            'total': len(history),
            'message': f'获取到 {len(history)} 条切换历史记录'
        })
        
    except Exception as e:
        logger.error(f"❌ 获取切换历史失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取切换历史失败: {str(e)}',
            'data': []
        }), 500

@digital_twin_api.route('/api/v2/equipment/health-summary', methods=['GET'])
@login_required
def get_health_summary():
    """获取设备健康状态汇总"""
    try:
        from app.utils.db_helper import get_mysql_connection
        
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # 健康状态统计
        cursor.execute("""
            SELECT 
                overall_condition,
                COUNT(*) as count
            FROM v_equipment_real_time_status
            GROUP BY overall_condition
        """)
        condition_stats = cursor.fetchall()
        
        # 维护紧急程度统计
        cursor.execute("""
            SELECT 
                maintenance_urgency,
                COUNT(*) as count
            FROM equipment_digital_twin
            WHERE is_active = TRUE
            GROUP BY maintenance_urgency
        """)
        urgency_stats = cursor.fetchall()
        
        # 平均健康评分
        cursor.execute("""
            SELECT 
                AVG(health_score) as avg_health_score,
                MIN(health_score) as min_health_score,
                MAX(health_score) as max_health_score
            FROM equipment_digital_twin
            WHERE is_active = TRUE
        """)
        health_stats = cursor.fetchone()
        
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'data': {
                'condition_distribution': condition_stats,
                'urgency_distribution': urgency_stats,
                'health_statistics': health_stats
            },
            'message': '设备健康状态汇总获取成功'
        })
        
    except Exception as e:
        logger.error(f"❌ 获取健康状态汇总失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取健康状态汇总失败: {str(e)}',
            'data': {}
        }), 500

@digital_twin_api.route('/api/v2/data-collection/start', methods=['POST'])
@login_required
def start_data_collection():
    """启动数据采集服务"""
    try:
        data_collection_service.start_collection()
        
        return jsonify({
            'success': True,
            'message': '数据采集服务启动成功'
        })
        
    except Exception as e:
        logger.error(f"❌ 启动数据采集服务失败: {e}")
        return jsonify({
            'success': False,
            'message': f'启动数据采集服务失败: {str(e)}'
        }), 500

@digital_twin_api.route('/api/v2/data-collection/stop', methods=['POST'])
@login_required
def stop_data_collection():
    """停止数据采集服务"""
    try:
        data_collection_service.stop_collection()
        
        return jsonify({
            'success': True,
            'message': '数据采集服务停止成功'
        })
        
    except Exception as e:
        logger.error(f"❌ 停止数据采集服务失败: {e}")
        return jsonify({
            'success': False,
            'message': f'停止数据采集服务失败: {str(e)}'
        }), 500
