#!/usr/bin/env python3
"""
优先级配置字段名统一改为小写
解决Excel导入失败问题 - 统一字段名为小写格式
"""

import os
import sys
import pandas as pd
import shutil
from datetime import datetime

def backup_excel_files():
    """备份Excel文件"""
    backup_dir = f"excel_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    os.makedirs(backup_dir, exist_ok=True)
    
    excel_path = "Excellist2025.06.05"
    files_to_backup = [
        "devicepriorityconfig.xlsx",
        "lotpriorityconfig.xlsx"
    ]
    
    for file in files_to_backup:
        src = os.path.join(excel_path, file)
        dst = os.path.join(backup_dir, file)
        if os.path.exists(src):
            shutil.copy2(src, dst)
            print(f"✅ 备份文件: {src} -> {dst}")
    
    return backup_dir

def fix_lotpriorityconfig_excel():
    """修复lotpriorityconfig.xlsx - 将字段名改为小写"""
    excel_file = "Excellist2025.06.05/lotpriorityconfig.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ 文件不存在: {excel_file}")
        return False
    
    try:
        # 读取原始数据
        df = pd.read_excel(excel_file)
        print(f"📖 读取文件: {excel_file}")
        print(f"原始列名: {list(df.columns)}")
        
        # 字段名映射 - 大写转小写
        column_mapping = {
            'DEVICE': 'device',
            'STAGE': 'stage', 
            'PRIORITY': 'priority',
            'REFRESH_TIME': 'refresh_time',
            'USER': 'user'
        }
        
        # 重命名列
        df.rename(columns=column_mapping, inplace=True)
        print(f"新列名: {list(df.columns)}")
        
        # 保存文件
        df.to_excel(excel_file, index=False)
        print(f"✅ 成功更新文件: {excel_file}")
        return True
        
    except Exception as e:
        print(f"❌ 修复Excel文件失败: {e}")
        return False

def check_devicepriorityconfig_excel():
    """检查devicepriorityconfig.xlsx字段名"""
    excel_file = "Excellist2025.06.05/devicepriorityconfig.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ 文件不存在: {excel_file}")
        return False
    
    try:
        df = pd.read_excel(excel_file)
        print(f"📖 检查文件: {excel_file}")
        print(f"列名: {list(df.columns)}")
        
        # 检查是否已经是小写
        expected_columns = ['device', 'stage', 'handler_config', 'handler_priority', 
                          'setup_qty', 'priority', 'price', 'from_time', 'end_time', 
                          'refresh_time', 'user']
        
        actual_columns = list(df.columns)
        
        # 保留主要字段，排除多余字段
        main_columns = ['device', 'stage', 'priority', 'from_time', 'end_time', 'refresh_time', 'user']
        
        # 检查主要字段是否存在
        missing_columns = [col for col in main_columns if col not in actual_columns]
        if missing_columns:
            print(f"⚠️  缺少字段: {missing_columns}")
            return False
        
        print(f"✅ devicepriorityconfig.xlsx 字段名已是小写格式")
        return True
        
    except Exception as e:
        print(f"❌ 检查Excel文件失败: {e}")
        return False

def create_database_model_update():
    """生成数据库模型更新脚本"""
    script_content = '''
# 数据库模型更新脚本
# 将DevicePriorityConfig和LotPriorityConfig的字段名改为小写

# 1. 修改 app/models.py 中的模型定义
# DevicePriorityConfig 模型字段更新:
# ID -> id
# DEVICE -> device  
# PRIORITY -> priority
# FROM_TIME -> from_time
# END_TIME -> end_time
# REFRESH_TIME -> refresh_time
# USER -> user

# LotPriorityConfig 模型字段更新:
# ID -> id
# DEVICE -> device
# STAGE -> stage
# PRIORITY -> priority
# REFRESH_TIME -> refresh_time
# USER -> user

# 2. 修改 app/api_v2/production/routes.py 中的导入代码
# 将所有字段引用从大写改为小写

# 3. 创建数据库迁移脚本
'''
    
    with open("database_model_update_script.txt", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("✅ 创建数据库模型更新脚本: database_model_update_script.txt")

def main():
    """主函数"""
    print("🔧 开始修复优先级配置字段名问题...")
    print("=" * 60)
    
    # 1. 备份Excel文件
    print("1️⃣ 备份Excel文件...")
    backup_dir = backup_excel_files()
    print(f"备份目录: {backup_dir}")
    print()
    
    # 2. 检查devicepriorityconfig.xlsx
    print("2️⃣ 检查devicepriorityconfig.xlsx...")
    device_ok = check_devicepriorityconfig_excel()
    print()
    
    # 3. 修复lotpriorityconfig.xlsx
    print("3️⃣ 修复lotpriorityconfig.xlsx...")
    lot_ok = fix_lotpriorityconfig_excel()
    print()
    
    # 4. 生成数据库模型更新脚本
    print("4️⃣ 生成数据库模型更新脚本...")
    create_database_model_update()
    print()
    
    # 总结
    print("📋 修复结果总结:")
    print(f"  📁 Excel文件备份: {backup_dir}")
    print(f"  📄 devicepriorityconfig.xlsx: {'✅ 正常' if device_ok else '❌ 需要检查'}")
    print(f"  📄 lotpriorityconfig.xlsx: {'✅ 已修复' if lot_ok else '❌ 修复失败'}")
    print("  📝 数据库模型更新脚本: database_model_update_script.txt")
    print()
    
    if device_ok and lot_ok:
        print("🎉 Excel文件字段名已统一为小写格式！")
        print("💡 接下来需要:")
        print("   1. 更新数据库模型字段名")
        print("   2. 更新导入代码字段名") 
        print("   3. 重新测试Excel导入功能")
    else:
        print("⚠️ 部分修复失败，请检查错误信息")

if __name__ == "__main__":
    main() 