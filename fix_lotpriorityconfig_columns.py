#!/usr/bin/env python3
"""
修复lotpriorityconfig.xlsx的列名为小写
"""

import pandas as pd
import os
import shutil
from datetime import datetime

def fix_lotpriorityconfig():
    """修复lotpriorityconfig.xlsx - 将字段名改为小写"""
    excel_file = "Excellist2025.06.05/lotpriorityconfig.xlsx"
    backup_file = f"Excellist2025.06.05/lotpriorityconfig_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ 文件不存在: {excel_file}")
        return False
    
    try:
        # 备份原文件
        shutil.copy2(excel_file, backup_file)
        print(f"✅ 备份文件: {backup_file}")
        
        # 读取原始数据
        df = pd.read_excel(excel_file)
        print(f"📖 读取文件: {excel_file}")
        print(f"原始列名: {list(df.columns)}")
        
        # 字段名映射 - 大写转小写
        column_mapping = {
            'DEVICE': 'device',
            'STAGE': 'stage', 
            'PRIORITY': 'priority',
            'REFRESH_TIME': 'refresh_time',
            'USER': 'user'
        }
        
        # 重命名列
        df.rename(columns=column_mapping, inplace=True)
        print(f"新列名: {list(df.columns)}")
        
        # 删除原文件
        os.remove(excel_file)
        print(f"🗑️  删除原文件: {excel_file}")
        
        # 保存新文件
        df.to_excel(excel_file, index=False)
        print(f"✅ 成功保存新文件: {excel_file}")
        
        # 验证新文件
        df_verify = pd.read_excel(excel_file)
        print(f"📋 验证新文件列名: {list(df_verify.columns)}")
        print(f"📊 数据行数: {len(df_verify)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复Excel文件失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 修复lotpriorityconfig.xlsx列名...")
    success = fix_lotpriorityconfig()
    if success:
        print("🎉 成功！lotpriorityconfig.xlsx列名已改为小写")
    else:
        print("❌ 修复失败，请检查错误信息") 