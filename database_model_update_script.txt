
# 数据库模型更新脚本
# 将DevicePriorityConfig和LotPriorityConfig的字段名改为小写

# 1. 修改 app/models.py 中的模型定义
# DevicePriorityConfig 模型字段更新:
# ID -> id
# DEVICE -> device  
# PRIORITY -> priority
# FROM_TIME -> from_time
# END_TIME -> end_time
# REFRESH_TIME -> refresh_time
# USER -> user

# LotPriorityConfig 模型字段更新:
# ID -> id
# DEVICE -> device
# STAGE -> stage
# PRIORITY -> priority
# REFRESH_TIME -> refresh_time
# USER -> user

# 2. 修改 app/api_v2/production/routes.py 中的导入代码
# 将所有字段引用从大写改为小写

# 3. 创建数据库迁移脚本
