#!/usr/bin/env python3
"""
测试列名映射修复
验证模型字段与Excel列名的匹配
"""

import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_model_fields():
    """测试模型字段定义"""
    try:
        from app import create_app
        from app.models import DevicePriorityConfig, LotPriorityConfig
        
        app = create_app()
        
        with app.app_context():
            print("🔍 测试模型字段定义...")
            
            # 测试DevicePriorityConfig
            print("\n=== DevicePriorityConfig 字段 ===")
            device_fields = []
            for attr_name in dir(DevicePriorityConfig):
                if not attr_name.startswith('_') and hasattr(getattr(DevicePriorityConfig, attr_name), 'type'):
                    device_fields.append(attr_name)
            
            print(f"模型字段: {device_fields}")
            
            # 测试LotPriorityConfig
            print("\n=== LotPriorityConfig 字段 ===")
            lot_fields = []
            for attr_name in dir(LotPriorityConfig):
                if not attr_name.startswith('_') and hasattr(getattr(LotPriorityConfig, attr_name), 'type'):
                    lot_fields.append(attr_name)
            
            print(f"模型字段: {lot_fields}")
            
            # 测试创建实例
            print("\n=== 测试创建实例 ===")
            
            test_device = DevicePriorityConfig(
                DEVICE='TEST_DEVICE',
                PRIORITY='1',
                FROM_TIME=datetime.now(),
                END_TIME=datetime.now(),
                REFRESH_TIME=datetime.now(),
                USER='test_user'
            )
            
            print(f"DevicePriorityConfig实例: {test_device}")
            print(f"to_dict(): {test_device.to_dict()}")
            
            test_lot = LotPriorityConfig(
                DEVICE='TEST_DEVICE',
                STAGE='TEST_STAGE',
                PRIORITY='1',
                REFRESH_TIME=datetime.now(),
                USER='test_user'
            )
            
            print(f"LotPriorityConfig实例: {test_lot}")
            print(f"to_dict(): {test_lot.to_dict()}")
            
            return True
            
    except Exception as e:
        print(f"❌ 模型字段测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_excel_compatibility():
    """测试Excel兼容性"""
    print("\n🔍 测试Excel兼容性...")
    
    # 模拟Excel数据
    excel_data_device = {
        'DEVICE': 'IWO521DFND-M001_TR1',
        'PRIORITY': '1',
        'FROM_TIME': '2025-06-23 12:00:00',
        'END_TIME': '2025-06-24 12:00:00',
        'REFRESH_TIME': '2025-06-23 10:00:00',
        'USER': 'admin'
    }
    
    excel_data_lot = {
        'DEVICE': 'IWO521DFND-M001_TR1',
        'STAGE': 'FT',
        'PRIORITY': '1',
        'REFRESH_TIME': '2025-06-23 10:00:00',
        'USER': 'admin'
    }
    
    print(f"Excel设备数据: {excel_data_device}")
    print(f"Excel批次数据: {excel_data_lot}")
    
    # 验证字段匹配
    expected_device_fields = ['DEVICE', 'PRIORITY', 'FROM_TIME', 'END_TIME', 'REFRESH_TIME', 'USER']
    expected_lot_fields = ['DEVICE', 'STAGE', 'PRIORITY', 'REFRESH_TIME', 'USER']
    
    device_match = all(field in excel_data_device for field in expected_device_fields)
    lot_match = all(field in excel_data_lot for field in expected_lot_fields)
    
    print(f"设备字段匹配: {'✅' if device_match else '❌'}")
    print(f"批次字段匹配: {'✅' if lot_match else '❌'}")
    
    return device_match and lot_match

def test_api_processing():
    """测试API处理逻辑"""
    print("\n🔍 测试API处理逻辑...")
    
    try:
        import pandas as pd
        from app import create_app
        from app.models import DevicePriorityConfig, LotPriorityConfig
        
        app = create_app()
        
        with app.app_context():
            # 模拟Excel数据处理
            excel_data = {
                'DEVICE': ['TEST_DEVICE_1', 'TEST_DEVICE_2'],
                'PRIORITY': ['1', '2'],
                'FROM_TIME': ['2025-06-23 12:00:00', '2025-06-23 13:00:00'],
                'END_TIME': ['2025-06-24 12:00:00', '2025-06-24 13:00:00'],
                'REFRESH_TIME': ['2025-06-23 10:00:00', '2025-06-23 11:00:00'],
                'USER': ['admin', 'admin']
            }
            
            df = pd.DataFrame(excel_data)
            print(f"模拟Excel数据: {len(df)} 行")
            print(f"列名: {list(df.columns)}")
            
            # 模拟API处理
            for index, row in df.iterrows():
                try:
                    record = DevicePriorityConfig(
                        DEVICE=str(row.get('DEVICE', '')),
                        PRIORITY=str(row.get('PRIORITY', '5')),
                        FROM_TIME=pd.to_datetime(row.get('FROM_TIME')) if pd.notna(row.get('FROM_TIME')) else None,
                        END_TIME=pd.to_datetime(row.get('END_TIME')) if pd.notna(row.get('END_TIME')) else None,
                        REFRESH_TIME=pd.to_datetime(row.get('REFRESH_TIME')) if pd.notna(row.get('REFRESH_TIME')) else None,
                        USER=str(row.get('USER', '')) if pd.notna(row.get('USER')) else 'default_user'
                    )
                    
                    print(f"✅ 第{index+1}行处理成功: {record}")
                    
                except Exception as e:
                    print(f"❌ 第{index+1}行处理失败: {e}")
                    return False
            
            return True
            
    except Exception as e:
        print(f"❌ API处理测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 列名映射修复测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 1. 测试模型字段
    if test_model_fields():
        success_count += 1
        print("✅ 模型字段测试通过")
    else:
        print("❌ 模型字段测试失败")
    
    # 2. 测试Excel兼容性
    if test_excel_compatibility():
        success_count += 1
        print("✅ Excel兼容性测试通过")
    else:
        print("❌ Excel兼容性测试失败")
    
    # 3. 测试API处理
    if test_api_processing():
        success_count += 1
        print("✅ API处理测试通过")
    else:
        print("❌ API处理测试失败")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！列名映射修复成功！")
        print("\n📋 下一步操作:")
        print("1. 重启Flask应用")
        print("2. 测试Excel导入功能")
        print("3. 验证数据正确写入数据库")
    else:
        print("⚠️  部分测试失败，请检查修复内容")

if __name__ == "__main__":
    main()
