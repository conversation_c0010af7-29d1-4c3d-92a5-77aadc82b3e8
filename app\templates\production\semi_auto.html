{% extends "base.html" %}

{% block title %}AEC-FT ICP - 半自动排产数据收集
<style>
:root {
    --primary-color: #b72424;
    --secondary-color: #d73027;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.navbar-brand, .nav-link.active {
    color: var(--primary-color) !important;
}

.card-header {
    background-color: var(--primary-color);
    color: white;
}
</style>
{% endblock %}



{% block extra_css %}
<link href="{{ url_for('static', filename='vendor/fullcalendar/core/main.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='vendor/fullcalendar/daygrid/main.css') }}" rel="stylesheet" />
<link href="{{ url_for('static', filename='vendor/fullcalendar/timegrid/main.css') }}" rel="stylesheet" />

<style>
    .fc-event {
        cursor: pointer;
    }
    .resource-list {
        max-height: calc(100vh - 300px);
        overflow-y: auto;
    }
    .order-list {
        max-height: calc(100vh - 300px);
        overflow-y: auto;
    }
    .batch-upload {
        margin-bottom: 1rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    .priority-settings {
        margin-bottom: 1rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 4px;
    }
    /* 进度条容器样式 */
    .progress-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .progress-container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        width: 300px;
        text-align: center;
    }

    .progress {
        height: 20px;
        margin: 15px 0;
        border-radius: 5px;
        overflow: hidden;
        position: relative;
    }

    .progress-bar {
        background-color: #b72424 !important;
        height: 100%;
        position: relative;
    }

    .progress-text {
        margin-top: 10px;
        font-weight: bold;
    }

    .progress-percent {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        line-height: 20px;
        color: white;
        text-align: center;
        font-weight: bold;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
    }

    /* 修改表格字体大小和行高 */
    .table {
        font-size: 0.8rem;
        line-height: 1.2;
    }
    
    /* 调整单元格内边距 */
    .table td, 
    .table th {
        padding: 0.3rem 0.5rem;
        white-space: nowrap;
    }
    
    /* 固定表头 */
    .table-responsive {
        position: relative;
    }
    
    .table thead th {
        position: sticky;
        top: 0;
        z-index: 1;
        background-color: #f8f9fa;
    }
    
    /* 调整预览表格的最大高度 */
    .table-responsive {
        max-height: 400px;
        overflow-y: auto;
    }
    
    /* 确保水平滚动时内容清晰可见 */
    .table-responsive::-webkit-scrollbar {
        height: 8px;
        width: 8px;
    }
    
    .table-responsive::-webkit-scrollbar-track {
        background: #f1f1f1;
    }
    
    .table-responsive::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
    }
    
    .table-responsive::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    /* 修改主要按钮颜色 */
    .btn-primary {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:hover {
        background-color: #b72424;
        border-color: #b72424;
    }
    .btn-primary:focus {
        background-color: #b72424;
        border-color: #b72424;
        box-shadow: 0 0 0 0.25rem rgba(245, 34, 45, 0.25);
    }

    /* 修改信息按钮颜色 */
    .btn-info {
        background-color: #b72424;
        border-color: #b72424;
        color: white;
    }
    .btn-info:hover {
        background-color: #b54442;
        border-color: #b54442;
        color: white;
    }

    /* 修改徽章颜色 */
    .badge.bg-primary {
        background-color: #b72424 !important;
    }

    /* 修改链接颜色 */
    a {
        color: #b72424;
    }
    a:hover {
        color: #b72424;
    }

    /* 修改表格中的选中和悬停状态 */
    .table tbody tr:hover {
        background-color: #fff1f0;
    }
    .table tbody tr.selected {
        background-color: #fff1f0;
    }

    /* 修改分页激活状态颜色 */
    .page-item.active .page-link {
        background-color: #b72424;
        border-color: #b72424;
    }
    .page-link {
        color: #b72424;
    }
    .page-link:hover {
        color: #b72424;
    }

    /* 功能卡片样式 */
    .function-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        background-color: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }

    .function-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .function-card .card-icon {
        font-size: 2rem;
        margin-bottom: 10px;
        color: #b72424;
    }

    .function-card .card-title {
        font-weight: 600;
        margin-bottom: 10px;
    }

    .function-card .card-text {
        color: #666;
        margin-bottom: 15px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col">
            <div class="card">
                <div class="card-body">
                    <h4 class="mb-4">Excel数据导入管理中心</h4>
                    
                    <div class="row">
                        <!-- 批次上传 -->
                        <div class="col-md-5">
                            <div class="batch-upload">
                                <h6 class="mb-3">Excel文件上传</h6>
                                <div class="mb-3">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="importPath" 
                                               placeholder="请输入Excel文件所在路径">
                                        <input type="file" id="folderSelector" webkitdirectory directory style="display: none;">
                                        <button class="btn btn-outline-secondary" type="button" onclick="savePath()">
                                            <i class="fas fa-save"></i> 保存路径
                                        </button>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <button type="button" class="btn btn-primary" onclick="importFromDirectory()">
                                        <i class="fas fa-file-import me-1"></i>导入Excel文件
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 导出操作 -->
                        <div class="col-md-7">
                            <div class="priority-settings">
                                <h6 class="mb-3">数据操作</h6>
                                <div class="d-flex gap-2 mb-3">
                                    <button type="button" class="btn btn-info flex-grow-1" onclick="previewSchedule()">
                                        <i class="fas fa-eye me-1"></i>预览数据
                                    </button>
                                    <button type="button" class="btn btn-secondary flex-grow-1" onclick="exportSchedule()">
                                        <i class="fas fa-download me-1"></i>导出数据
                                    </button>
                                    <button type="button" class="btn btn-danger flex-grow-1" onclick="deleteSelectedFile()">
                                        <i class="fas fa-trash me-1"></i>删除当前数据
                                    </button>
                                </div>
                                
                                <!-- 🔥 新增手动排产功能区域 -->
                                <div class="manual-scheduling-section">
                                    <h6 class="mb-3">
                                        <i class="fas fa-cogs me-1"></i>手动排产
                                    </h6>
                                    
                                    <!-- 排产策略选择 -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">排产策略</label>
                                            <select class="form-select form-select-sm" id="manualScheduleStrategy">
                                                <option value="deadline">交期优先策略</option>
                                                <option value="product">产品优先策略</option>
                                                <option value="value">产值优先策略</option>
                                                <option value="intelligent" selected>智能综合策略</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">优化目标</label>
                                            <select class="form-select form-select-sm" id="manualOptimizationTarget">
                                                <option value="makespan">最小化完工时间</option>
                                                <option value="balanced" selected>均衡优化</option>
                                                <option value="efficiency">最大化效率</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <!-- 操作按钮 -->
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-primary flex-grow-1" onclick="executeManualScheduling()">
                                            <i class="fas fa-play me-1"></i>手动排产
                                        </button>
                                        <button type="button" class="btn btn-success flex-grow-1" onclick="saveScheduleResult()" disabled id="saveScheduleBtn">
                                            <i class="fas fa-save me-1"></i>保存排序
                                        </button>
                                        <button type="button" class="btn btn-info flex-grow-1" onclick="viewScheduleHistory()">
                                            <i class="fas fa-history me-1"></i>历史记录
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- 数据预览区域 -->
    <div class="mt-3">
        <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="mb-0">数据预览</h6>
            <div class="d-flex gap-2">
                <select class="form-select form-select-sm" id="fileSelector" style="width: 200px;">
                    <option value="">选择数据</option>
                </select>
                <button class="btn btn-sm btn-primary" onclick="refreshTableData()">
                    <i class="fas fa-sync-alt"></i> 刷新
                </button>
            </div>
        </div>
        <div class="table-responsive" style="max-height: 400px;">
            <table class="table table-sm table-hover" id="previewTable">
                <thead id="previewTableHeader">
                    <!-- 表头将动态生成 -->
                </thead>
                <tbody id="previewTableBody">
                    <!-- 表体将动态生成 -->
                </tbody>
            </table>
        </div>
        <!-- 添加分页导航 -->
        <nav>
            <ul class="pagination justify-content-center mt-3" id="tablePagination"></ul>
        </nav>
    </div>

    <!-- 🔥 新增排产结果显示区域 -->
    <div class="mt-4" id="scheduleResultSection" style="display: none;">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-list-alt me-1"></i>排产结果
                        <span class="badge bg-primary ms-2" id="scheduleResultCount">0</span>
                    </h6>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-success" onclick="exportScheduleResult()">
                            <i class="fas fa-file-excel me-1"></i>导出Excel
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="clearScheduleResult()">
                            <i class="fas fa-times me-1"></i>清除结果
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- 排产统计信息 -->
                <div class="row mb-3" id="scheduleMetrics">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h5 mb-0 text-primary" id="totalBatches">0</div>
                            <small class="text-muted">总批次数</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h5 mb-0 text-success" id="scheduledBatches">0</div>
                            <small class="text-muted">已排产批次</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h5 mb-0 text-info" id="usedStrategy">-</div>
                            <small class="text-muted">排产策略</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h5 mb-0 text-warning" id="executionTime">0s</div>
                            <small class="text-muted">执行时间</small>
                        </div>
                    </div>
                </div>
                
                <!-- 排产结果表格 -->
                <div class="table-responsive" style="max-height: 500px;">
                    <table class="table table-sm table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>ORDER</th>
                                <th>HANDLER_ID</th>
                                <th>LOT_ID</th>
                                <th>LOT_TYPE</th>
                                <th>GOOD_QTY</th>
                                <th>PROD_ID</th>
                                <th>DEVICE</th>
                                <th>CHIP_ID</th>
                                <th>PKG_PN</th>
                                <th>PO_ID</th>
                                <th>STAGE</th>
                                <th>WIP_STATE</th>
                                <th>PROC_STATE</th>
                                <th>HOLD_STATE</th>
                                <th>FLOW_ID</th>
                                <th>FLOW_VER</th>
                                <th>RELEASE_TIME</th>
                                <th>FAC_ID</th>
                                <th>CREATE_TIME</th>
                            </tr>
                        </thead>
                        <tbody id="scheduleResultTableBody">
                            <!-- 排产结果将动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- 🔥 历史记录模态框 -->
    <div class="modal fade" id="scheduleHistoryModal" tabindex="-1" aria-labelledby="scheduleHistoryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="scheduleHistoryModalLabel">
                        <i class="fas fa-history me-2"></i>排产历史记录
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive" style="max-height: 600px;">
                        <table class="table table-sm table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>时间</th>
                                    <th>排产策略</th>
                                    <th>批次数量</th>
                                    <th>执行时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- 历史记录将动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-danger" onclick="clearAllHistory()">
                        <i class="fas fa-trash me-1"></i>清空历史
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 进度条 HTML -->
    <div class="progress-overlay" id="progressOverlay">
        <div class="progress-container">
            <div id="progressText" class="mb-2">正在导入...</div>
            <div class="progress">
                <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
                <div id="progressPercent" class="progress-percent">0%</div>
            </div>
        </div>
    </div>
{% endblock %}
{% block extra_js %}
<script src="{{ url_for('static', filename='vendor/xlsx/xlsx.full.min.js') }}"></script>
<script>

// 存储导入的数据
let importedData = {
    fileData: {},
    currentFile: null
};

// 从固定目录导入数据
function importFromDirectory() {
    let path = document.getElementById('importPath').value.trim();
    if (!path) {
        alert('请输入Excel文件所在路径');
        return;
    }
    
    // 重置错误计数器
    window.progressErrorCount = 0;
    
    // 显示进度条
    showProgress(true);
    updateProgress(0, '正在准备导入...');
    
    // 规范化路径格式
    path = path.replace(/\\/g, '/');
    
    // 声明进度轮询器变量
    let progressChecker;
    
    // 先清理旧的进度文件
    fetch('/api/production/clear-import-progress', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(() => {
        updateProgress(0, '正在导入Excel文件...');
        
        // 启动进度轮询
        progressChecker = startProgressPolling();
        
        return fetch('/api/production/import-from-directory', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ path })
        });
    })
    .then(response => {
        if (!response.ok) {
            // 尝试解析错误响应
            return response.json().then(errorData => {
                const errorMessage = `HTTP ${response.status}: ${errorData.error || errorData.message || '服务器内部错误'}`;
                throw new Error(errorMessage);
            }).catch(() => {
                throw new Error(`HTTP ${response.status}: 服务器响应错误，无法解析错误信息`);
            });
        }
        return response.json();
    })
    .then(result => {
        if (result.error) {
            throw new Error(result.error);
        }
        
        // 停止进度轮询
        clearInterval(progressChecker);
        
        // 更新进度到100%
        updateProgress(100, '导入完成');
        
        setTimeout(() => {
            showProgress(false);
            
            // 构建更专业的导入结果信息
            let message = '🎉 Excel文件导入完成\n\n';
            message += `📋 导入状态：${result.message}\n\n`;
            
            // 添加详细的表导入统计
            if (result.details) {
                message += `📊 详细信息：${result.details}\n\n`;
            }
            
            // 添加总体统计摘要
            const totalFiles = result.total_files || 0;
            const successFiles = result.processed_files ? result.processed_files.length : 0;
            const failedFiles = result.failed_count || 0;
            const totalRecords = result.total_records || 0;
            const processingTime = result.processing_time || 0;
            
            message += '📈 导入统计摘要：\n';
            message += `  • 总文件数：${totalFiles}\n`;
            message += `  • 成功导入：${successFiles} 个文件\n`;
            message += `  • 导入失败：${failedFiles} 个文件\n`;
            message += `  • 总记录数：${totalRecords} 条\n`;
            message += `  • 处理耗时：${processingTime} 秒\n\n`;
            
            // 添加处理的文件列表和每个文件导入的记录数
            if (result.processed_files && result.processed_files.length > 0) {
                message += "✅ 成功导入的文件详情：\n";
                const processedFiles = [];
                
                result.processed_files.forEach(file => {
                    message += `  📄 ${file.file} → ${file.table}表：${file.records}条记录\n`;
                    processedFiles.push({
                        name: file.file,
                        table: file.table,
                        records: file.records
                    });
                });
                
                message += "\n";
                
                // 更新文件选择器
                updateFileSelector(processedFiles);
            }
            
            // 添加失败文件信息
            if (result.failed_files && result.failed_files.length > 0) {
                message += "❌ 导入失败的文件详情：\n";
                result.failed_files.forEach(file => {
                    message += `  📄 ${file.file}\n`;
                    message += `     错误：${file.error}\n`;
                });
                message += "\n";
                
                // 添加失败文件的解决建议
                message += "💡 失败文件处理建议：\n";
                message += "  • 检查文件格式是否正确\n";
                message += "  • 确认文件未被其他程序占用\n";
                message += "  • 验证文件内容是否符合导入规范\n\n";
            }
            
            // 添加下一步操作建议
            if (successFiles > 0) {
                message += "🎯 下一步操作：\n";
                message += "  • 可以开始进行智能排产\n";
                message += "  • 检查导入的数据是否正确\n";
                message += "  • 设置排产参数和策略\n";
            }
            
            // 刷新表格数据（放在弹窗之前执行）
            loadImportedFiles();
            
            // 强制显示导入结果总结弹窗
            alert(message);
        }, 500);
    })
    .catch(error => {
        // 停止进度轮询
        clearInterval(progressChecker);
        
        showProgress(false);
        console.error('导入错误:', error);
        
        // 构建详细的错误信息
        let errorMessage = '📋 Excel文件导入失败\n\n';
        errorMessage += `❌ 错误详情：${error.message || '未知错误'}\n\n`;
        errorMessage += '🔍 可能的解决方案：\n';
        errorMessage += '1. 检查文件路径是否正确\n';
        errorMessage += '2. 确认对该目录有读取权限\n';
        errorMessage += '3. 验证目录中包含正确格式的Excel文件\n';
        errorMessage += '4. 检查Excel文件是否被其他程序占用\n';
        errorMessage += '5. 确认文件名不包含特殊字符\n';
        errorMessage += '6. 检查网络连接是否正常\n\n';
        errorMessage += '💡 提示：如果问题持续存在，请联系系统管理员或查看服务器日志。';
        
        alert(errorMessage);
    });
}

// 开始轮询进度
function startProgressPolling() {
    // 每500毫秒检查一次进度
    const intervalId = setInterval(() => {
        fetch('/api/production/import-progress')
            .then(response => response.json())
            .then(data => {
                // 重置错误计数器
                window.progressErrorCount = 0;
                
                // 构建当前文件进度信息
                const currentFileProgress = {
                    current_file: data.current_file,
                    files_processed: data.files_processed || 0,
                    total_files: data.total_files || 0
                };
                
                // 更新进度条，包括当前文件进度
                updateProgress(data.percent, data.message, currentFileProgress);
                
                // 如果有错误，显示详细错误消息
                if (data.error) {
                    showProgress(false);
                    
                    let errorMessage = '📋 Excel文件导入过程中出现错误\n\n';
                    errorMessage += `❌ 错误详情：${data.message || '未知错误'}\n\n`;
                    
                    if (data.current_file) {
                        errorMessage += `📄 当前处理文件：${data.current_file}\n`;
                    }
                    
                    if (data.files_processed && data.total_files) {
                        errorMessage += `📊 处理进度：${data.files_processed}/${data.total_files} 个文件\n\n`;
                    }
                    
                    errorMessage += '🔍 可能的解决方案：\n';
                    errorMessage += '  • 检查当前文件格式是否正确\n';
                    errorMessage += '  • 确认文件未被占用\n';
                    errorMessage += '  • 验证文件权限设置\n';
                    errorMessage += '  • 重新尝试导入操作\n';
                    
                    alert(errorMessage);
                    clearInterval(intervalId);
                }
                
                // 如果状态是completed，显示完成信息但继续轮询
                if (data.status === 'completed') {
                    updateProgress(100, '导入已完成');
                }
                // 如果状态是idle，停止轮询
                else if (data.status === 'idle') {
                    clearInterval(intervalId);
                    // 不更新进度显示，保持当前的导入结果显示
                    return;
                }
                // 如果进度是100%，停止轮询
                else if (data.percent >= 100) {
                    clearInterval(intervalId);
                }
            })
            .catch(error => {
                console.error('获取进度失败:', error);
                // 如果连续多次获取进度失败，停止轮询
                if (!window.progressErrorCount) {
                    window.progressErrorCount = 0;
                }
                window.progressErrorCount++;
                
                if (window.progressErrorCount >= 10) {
                    console.warn('进度获取连续失败，停止轮询');
                    clearInterval(intervalId);
                    showProgress(false);
                    
                    let progressErrorMessage = '⚠️ 无法获取导入进度信息\n\n';
                    progressErrorMessage += '📋 当前状态：导入可能仍在后台运行\n\n';
                    progressErrorMessage += '🔄 建议操作：\n';
                    progressErrorMessage += '  • 稍等片刻后刷新页面\n';
                    progressErrorMessage += '  • 检查网络连接状态\n';
                    progressErrorMessage += '  • 查看是否有新数据导入\n';
                    progressErrorMessage += '  • 如果长时间无响应，请联系技术支持\n\n';
                    progressErrorMessage += '💡 提示：即使进度显示失败，文件导入可能已成功完成。';
                    
                    alert(progressErrorMessage);
                }
            });
    }, 500);
    
    return intervalId;
}

// 使用详细信息更新文件列表
function updateFileListWithDetails(files) {
    // 由于不再显示文件列表，此函数保留但不执行任何操作
    console.log('已导入文件:', files);
}

// 保存路径
function savePath() {
    const path = document.getElementById('importPath').value.trim();
    if (!path) {
        alert('请先输入路径');
        return;
    }
    
    fetch('/api/production/save-import-path', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ path })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('路径保存成功');
        } else {
            throw new Error(result.error || '保存失败');
        }
    })
    .catch(error => {
        console.error('保存错误:', error);
        alert('保存失败：' + error.message);
    });
}

// 页面加载时自动加载保存的路径和已导入文件
document.addEventListener('DOMContentLoaded', function() {
    loadSavedPath();
    loadImportedFiles();
    
    // 添加分页点击事件委托
    document.getElementById('tablePagination').addEventListener('click', function(e) {
        e.preventDefault();
        
        // 检查点击的是否是分页链接
        const pageLink = e.target.closest('.page-link');
        if (!pageLink) return;
        
        // 获取页码和表名
        const page = pageLink.dataset.page;
        const tableName = pageLink.dataset.table;
        
        // 检查是否是禁用状态的链接
        if (pageLink.parentElement.classList.contains('disabled')) return;
        
        // 加载对应页的数据
        loadFileData(tableName, parseInt(page));
    });
});

// 加载保存的路径
function loadSavedPath() {
    fetch('/api/production/get-import-path')
        .then(response => response.json())
        .then(result => {
            if (result.success && result.path) {
                document.getElementById('importPath').value = result.path;
            }
        })
        .catch(error => {
            console.error('加载保存的路径失败:', error);
        });
}

// 加载已导入的文件列表
function loadImportedFiles() {
    fetch('/api/production/imported-files')
        .then(response => response.json())
        .then(result => {
            if (result.success && result.files) {
                updateFileSelector(result.files);
            }
        })
        .catch(error => {
            console.error('加载已导入文件失败:', error);
        });
}

// 更新文件选择器
function updateFileSelector(files) {
    const fileSelector = document.getElementById('fileSelector');
    fileSelector.innerHTML = '<option value="">选择文件</option>' +
        files.map(file => 
            `<option value="${file.name}">${file.name}</option>`
        ).join('');
}

// 文件选择器变化时更新预览表格
document.getElementById('fileSelector').addEventListener('change', function(e) {
    const filename = e.target.value;
    if (!filename) {
        clearPreviewTable();
        return;
    }
    
    // 加载选中文件的数据
    loadFileData(filename);
});

// 加载文件数据
function loadFileData(filename, page = 1) {
    if (!filename) return;
    
    document.getElementById('previewTableBody').innerHTML = '<tr><td colspan="100%" class="text-center">加载中...</td></tr>';
    
    fetch(`/api/production/file-data/${filename}?page=${page}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // 更新表头
                const header = document.getElementById('previewTableHeader');
                header.innerHTML = `<tr>${result.columns.map(col => `<th>${col}</th>`).join('')}</tr>`;
                
                // 更新表体
                const body = document.getElementById('previewTableBody');
                body.innerHTML = result.data.map(row => `
                    <tr>${result.columns.map(col => `<td>${row[col] || ''}</td>`).join('')}</tr>
                `).join('');
                
                // 更新分页
                updateTablePagination(result.page, result.total_pages, filename);
            } else {
                throw new Error(result.error || '加载数据失败');
            }
        })
        .catch(error => {
            console.error('加载文件数据失败:', error);
            document.getElementById('previewTableBody').innerHTML = 
                '<tr><td colspan="100%" class="text-center text-danger">加载失败</td></tr>';
        });
}

// 更新预览表格
function updatePreviewTable(data, columns) {
    const thead = document.querySelector('#previewTable thead');
    const tbody = document.querySelector('#previewTable tbody');
    
    thead.innerHTML = `<tr>${columns.map(col => `<th>${col}</th>`).join('')}</tr>`;
    tbody.innerHTML = data.map(row => `
        <tr>${columns.map(col => `<td>${row[col] || ''}</td>`).join('')}</tr>
    `).join('');
}

// 清空预览表格
function clearPreviewTable() {
    document.querySelector('#previewTable thead').innerHTML = '';
    document.querySelector('#previewTable tbody').innerHTML = '';
}

// 更新分页
function updateTablePagination(currentPage, totalPages, tableName) {
    const pagination = document.getElementById('tablePagination');
    let html = '';
    
    if (totalPages > 1) {
        // 上一页
        html += `
            <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage - 1}" data-table="${tableName}">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
        
        // 页码
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, currentPage + 2);
        
        if (startPage > 1) {
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" data-page="1" data-table="${tableName}">1</a>
                </li>
            `;
            if (startPage > 2) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" data-page="${i}" data-table="${tableName}">${i}</a>
                </li>
            `;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" data-page="${totalPages}" data-table="${tableName}">${totalPages}</a>
                </li>
            `;
        }
        
        // 下一页
        html += `
            <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${currentPage + 1}" data-table="${tableName}">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
    }
    
    pagination.innerHTML = html;
}

// 刷新表格数据
async function refreshTableData() {
    try {
        // 先记住当前选中的文件
        const currentFile = document.getElementById('fileSelector').value;
        
        // 刷新文件列表
        await loadImportedFiles();
        
        // 如果有选中的文件，刷新表格数据
        if (currentFile) {
            loadFileData(currentFile);
        }
    } catch (error) {
        console.error('刷新失败:', error);
        alert('刷新失败，请重试');
    }
}

// 显示进度条
function showProgress(show = true) {
    const overlay = document.getElementById('progressOverlay');
    overlay.style.display = show ? 'flex' : 'none';
}

// 更新进度条
function updateProgress(percent, text, currentFileProgress) {
    const progressBar = document.getElementById('progressBar');
    const progressText = document.getElementById('progressText');
    const percentValue = Math.round(percent);
    
    progressBar.style.width = `${percent}%`;
    
    // 更新百分比显示
    progressText.textContent = text;
    
    // 更新进度条中的百分比文本
    let percentText = document.getElementById('progressPercent');
    if (!percentText) {
        percentText = document.createElement('div');
        percentText.id = 'progressPercent';
        percentText.className = 'progress-percent';
        percentText.style.cssText = `
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            z-index: 10;
        `;
        document.querySelector('.progress').style.position = 'relative';
        document.querySelector('.progress').appendChild(percentText);
    }
    percentText.textContent = `${percentValue}%`;
    
    // 如果有当前文件进度信息，显示更详细的信息
    if (currentFileProgress && currentFileProgress.current_file) {
        const detailText = `${text} (当前: ${currentFileProgress.current_file})`;
        progressText.textContent = detailText;
    }
}

// 预览数据
function previewSchedule() {
    const selectedFile = document.getElementById('fileSelector').value;
    if (!selectedFile) {
        alert('请先选择一个文件');
        return;
    }
    
    loadFileData(selectedFile);
}

// 导出数据
function exportSchedule() {
    const selectedFile = document.getElementById('fileSelector').value;
    if (!selectedFile) {
        alert('请先选择一个文件');
        return;
    }
    
    fetch(`/api/production/export-file/${selectedFile}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.blob();
        })
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = selectedFile;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            alert('导出成功');
        })
        .catch(error => {
            console.error('导出错误:', error);
            alert('导出失败：' + error.message);
        });
}

// 删除选中的文件
function deleteSelectedFile() {
    const selectedFile = document.getElementById('fileSelector').value;
    if (!selectedFile) {
        alert('请先选择一个文件');
        return;
    }
    
    if (!confirm(`确定要删除选中的文件 ${selectedFile} 吗？`)) {
        return;
    }
    
    fetch('/api/production/delete-file', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ file: selectedFile })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(`成功删除文件 ${selectedFile}`);
            refreshTableData();
        } else {
            throw new Error(result.error || '删除失败');
        }
    })
    .catch(error => {
        console.error('删除错误:', error);
        alert('删除失败：' + error.message);
    });
}

// ==================== 🔥 手动排产功能实现 ====================

// 全局变量存储排产结果
let currentScheduleResult = null;

// 执行手动排产
function executeManualScheduling() {
    const strategy = document.getElementById('manualScheduleStrategy').value;
    const target = document.getElementById('manualOptimizationTarget').value;
    
    // 显示进度条
    showProgress(true);
    updateProgress(0, '正在初始化排产参数...', null);
    
    const requestData = {
        algorithm: strategy,
        optimization_target: target,
        time_limit: 30,
        population_size: 100,
        auto_mode: false
    };
    
    // 模拟进度更新
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 90) progress = 90;
        updateProgress(progress, '正在执行排产算法...', null);
    }, 200);
    
    fetch('/api/production/auto-schedule', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(result => {
        clearInterval(progressInterval);
        
        if (result.success && result.schedule && result.schedule.length > 0) {
            updateProgress(100, '排产完成！', null);
            
            setTimeout(() => {
                showProgress(false);
                
                // 存储排产结果
                currentScheduleResult = result;
                
                // 显示排产结果
                displayScheduleResult(result);
                
                // 启用保存按钮
                document.getElementById('saveScheduleBtn').disabled = false;
                
                // 显示成功消息
                showNotification('排产完成！', `成功生成 ${result.schedule.length} 条排产记录`, 'success');
                
                // 询问是否查看已排产表
                setTimeout(() => {
                    if (confirm('排产完成！是否立即查看已排产表？')) {
                        window.open('/production/done-lots', '_blank');
                    }
                }, 1000);
            }, 1000);
        } else {
            clearInterval(progressInterval);
            showProgress(false);
            showNotification('排产失败', result.message || '没有生成有效的排产记录', 'error');
        }
    })
    .catch(error => {
        clearInterval(progressInterval);
        showProgress(false);
        console.error('排产错误:', error);
        showNotification('排产失败', '网络错误或服务器异常', 'error');
    });
}

// 显示排产结果
function displayScheduleResult(result) {
    const section = document.getElementById('scheduleResultSection');
    const tbody = document.getElementById('scheduleResultTableBody');
    
    // 更新统计信息
    document.getElementById('scheduleResultCount').textContent = result.schedule.length;
    document.getElementById('totalBatches').textContent = result.metrics?.total_batches || result.schedule.length;
    document.getElementById('scheduledBatches').textContent = result.schedule.length;
    
    // 策略名称映射
    const strategyNames = {
        'deadline': '交期优先',
        'product': '产品优先', 
        'value': '产值优先',
        'intelligent': '智能综合'
    };
    document.getElementById('usedStrategy').textContent = strategyNames[result.metrics?.algorithm] || '未知';
    document.getElementById('executionTime').textContent = `${(result.execution_time || 0).toFixed(2)}s`;
    
    // 生成表格内容
    let html = '';
    result.schedule.forEach((item, index) => {
        html += `
            <tr>
                <td>${index + 1}</td>
                <td>${item.HANDLER_ID || ''}</td>
                <td>${item.LOT_ID || ''}</td>
                <td>${item.LOT_TYPE || ''}</td>
                <td>${item.GOOD_QTY || ''}</td>
                <td>${item.PROD_ID || ''}</td>
                <td>${item.DEVICE || ''}</td>
                <td>${item.CHIP_ID || ''}</td>
                <td>${item.PKG_PN || ''}</td>
                <td>${item.PO_ID || ''}</td>
                <td>${item.STAGE || ''}</td>
                <td>${item.WIP_STATE || ''}</td>
                <td>${item.PROC_STATE || ''}</td>
                <td>${item.HOLD_STATE || ''}</td>
                <td>${item.FLOW_ID || ''}</td>
                <td>${item.FLOW_VER || ''}</td>
                <td>${item.RELEASE_TIME || ''}</td>
                <td>${item.FAC_ID || ''}</td>
                <td>${item.CREATE_TIME || ''}</td>
            </tr>
        `;
    });
    
    tbody.innerHTML = html;
    section.style.display = 'block';
    
    // 滚动到结果区域
    section.scrollIntoView({ behavior: 'smooth' });
}

// 获取优先级徽章样式
function getPriorityBadgeClass(priority) {
    switch(priority) {
        case 'high': return 'bg-danger';
        case 'medium': return 'bg-warning';
        case 'low': return 'bg-secondary';
        default: return 'bg-info';
    }
}

// 保存排产结果
function saveScheduleResult() {
    if (!currentScheduleResult || !currentScheduleResult.schedule) {
        alert('没有可保存的排产结果');
        return;
    }
    
    // 转换数据格式以匹配现有API
    const records = currentScheduleResult.schedule.map((item, index) => ({
        ORDER: index + 1,
        HANDLER_ID: item.HANDLER_ID || '',
        LOT_ID: item.LOT_ID || '',
        LOT_TYPE: item.LOT_TYPE || '',
        GOOD_QTY: item.GOOD_QTY || '',
        PROD_ID: item.PROD_ID || '',
        DEVICE: item.DEVICE || '',
        CHIP_ID: item.CHIP_ID || '',
        PKG_PN: item.PKG_PN || '',
        PO_ID: item.PO_ID || '',
        STAGE: item.STAGE || '',
        WIP_STATE: item.WIP_STATE || '',
        PROC_STATE: item.PROC_STATE || '',
        HOLD_STATE: item.HOLD_STATE || '',
        FLOW_ID: item.FLOW_ID || '',
        FLOW_VER: item.FLOW_VER || '',
        RELEASE_TIME: item.RELEASE_TIME || '',
        FAC_ID: item.FAC_ID || '',
        CREATE_TIME: item.CREATE_TIME || ''
    }));
    
    // 显示保存中状态
    const saveBtn = document.getElementById('saveScheduleBtn');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>保存中...';
    saveBtn.disabled = true;
    
    fetch('/api/production/save-priority-done', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ records: records })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification('保存成功', `已保存 ${records.length} 条排产记录到已排产表`, 'success');
            
            // 保存到历史记录
            saveToHistory(currentScheduleResult);
            
            // 询问是否查看已排产表
            setTimeout(() => {
                if (confirm('保存成功！是否立即查看已排产表？')) {
                    window.open('/production/done-lots', '_blank');
                }
            }, 1000);
        } else {
            throw new Error(result.error || '保存失败');
        }
    })
    .catch(error => {
        console.error('保存错误:', error);
        showNotification('保存失败', error.message || '保存失败，请重试', 'error');
    })
    .finally(() => {
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    });
}

// 保存到历史记录
function saveToHistory(scheduleResult) {
    const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
    
    const historyItem = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        strategy: scheduleResult.metrics?.algorithm || 'unknown',
        batchCount: scheduleResult.schedule.length,
        executionTime: scheduleResult.execution_time || 0,
        data: scheduleResult
    };
    
    history.unshift(historyItem); // 添加到开头
    
    // 只保留最近50条记录
    if (history.length > 50) {
        history.splice(50);
    }
    
    localStorage.setItem('scheduleHistory', JSON.stringify(history));
}

// 查看历史记录
function viewScheduleHistory() {
    const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
    const tbody = document.getElementById('historyTableBody');
    
    if (history.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无历史记录</td></tr>';
    } else {
        const strategyNames = {
            'deadline': '交期优先',
            'product': '产品优先',
            'value': '产值优先',
            'intelligent': '智能综合'
        };
        
        let html = '';
        history.forEach(item => {
            const date = new Date(item.timestamp);
            html += `
                <tr>
                    <td>${date.toLocaleString()}</td>
                    <td>${strategyNames[item.strategy] || item.strategy}</td>
                    <td>${item.batchCount}</td>
                    <td>${item.executionTime.toFixed(2)}s</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="loadHistoryItem(${item.id})">
                            <i class="fas fa-eye me-1"></i>查看
                        </button>
                        <button class="btn btn-sm btn-outline-danger ms-1" onclick="deleteHistoryItem(${item.id})">
                            <i class="fas fa-trash me-1"></i>删除
                        </button>
                    </td>
                </tr>
            `;
        });
        tbody.innerHTML = html;
    }
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('scheduleHistoryModal'));
    modal.show();
}

// 加载历史记录项
function loadHistoryItem(id) {
    const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
    const item = history.find(h => h.id === id);
    
    if (item) {
        currentScheduleResult = item.data;
        displayScheduleResult(item.data);
        document.getElementById('saveScheduleBtn').disabled = false;
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleHistoryModal'));
        modal.hide();
        
        showNotification('历史记录已加载', '可以重新保存或导出此结果', 'info');
    }
}

// 删除历史记录项
function deleteHistoryItem(id) {
    if (!confirm('确定要删除这条历史记录吗？')) {
        return;
    }
    
    const history = JSON.parse(localStorage.getItem('scheduleHistory') || '[]');
    const newHistory = history.filter(h => h.id !== id);
    localStorage.setItem('scheduleHistory', JSON.stringify(newHistory));
    
    // 刷新历史记录显示
    viewScheduleHistory();
}

// 清空所有历史记录
function clearAllHistory() {
    if (!confirm('确定要清空所有历史记录吗？此操作不可恢复！')) {
        return;
    }
    
    localStorage.removeItem('scheduleHistory');
    viewScheduleHistory();
    showNotification('历史记录已清空', '', 'info');
}

// 导出排产结果
function exportScheduleResult() {
    if (!currentScheduleResult || !currentScheduleResult.schedule) {
        alert('没有可导出的排产结果');
        return;
    }
    
    // 准备导出数据
    const exportData = currentScheduleResult.schedule.map((item, index) => ({
        'ORDER': index + 1,
        'HANDLER_ID': item.HANDLER_ID || '',
        'LOT_ID': item.LOT_ID || '',
        'LOT_TYPE': item.LOT_TYPE || '',
        'GOOD_QTY': item.GOOD_QTY || '',
        'PROD_ID': item.PROD_ID || '',
        'DEVICE': item.DEVICE || '',
        'CHIP_ID': item.CHIP_ID || '',
        'PKG_PN': item.PKG_PN || '',
        'PO_ID': item.PO_ID || '',
        'STAGE': item.STAGE || '',
        'WIP_STATE': item.WIP_STATE || '',
        'PROC_STATE': item.PROC_STATE || '',
        'HOLD_STATE': item.HOLD_STATE || '',
        'FLOW_ID': item.FLOW_ID || '',
        'FLOW_VER': item.FLOW_VER || '',
        'RELEASE_TIME': item.RELEASE_TIME || '',
        'FAC_ID': item.FAC_ID || '',
        'CREATE_TIME': item.CREATE_TIME || ''
    }));
    
    // 使用XLSX库导出
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(exportData);
    
    // 设置列宽
    const colWidths = [
        { wch: 8 },  // ORDER
        { wch: 12 }, // HANDLER_ID
        { wch: 15 }, // LOT_ID
        { wch: 12 }, // LOT_TYPE
        { wch: 10 }, // GOOD_QTY
        { wch: 12 }, // PROD_ID
        { wch: 15 }, // DEVICE
        { wch: 15 }, // CHIP_ID
        { wch: 15 }, // PKG_PN
        { wch: 12 }, // PO_ID
        { wch: 10 }, // STAGE
        { wch: 12 }, // WIP_STATE
        { wch: 12 }, // PROC_STATE
        { wch: 12 }, // HOLD_STATE
        { wch: 12 }, // FLOW_ID
        { wch: 10 }, // FLOW_VER
        { wch: 18 }, // RELEASE_TIME
        { wch: 8 },  // FAC_ID
        { wch: 18 }  // CREATE_TIME
    ];
    ws['!cols'] = colWidths;
    
    XLSX.utils.book_append_sheet(wb, ws, "排产结果");
    
    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    const filename = `手动排产结果_${timestamp}.xlsx`;
    
    XLSX.writeFile(wb, filename);
    showNotification('导出成功', `文件已保存为: ${filename}`, 'success');
}

// 清除排产结果
function clearScheduleResult() {
    if (!confirm('确定要清除当前排产结果吗？')) {
        return;
    }
    
    currentScheduleResult = null;
    document.getElementById('scheduleResultSection').style.display = 'none';
    document.getElementById('saveScheduleBtn').disabled = true;
    
    showNotification('结果已清除', '', 'info');
}

// 显示通知消息
function showNotification(title, message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        max-width: 500px;
    `;
    
    notification.innerHTML = `
        <strong>${title}</strong>
        ${message ? `<br><small>${message}</small>` : ''}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}
</script>
{% endblock %} 