#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数字孪生系统初始化脚本
创建数据库表结构，初始化设备数字孪生，启动数据采集
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.services.digital_twin_service import DigitalTwinService
from app.services.data_collection_service import DataCollectionService
from app.utils.db_helper import get_mysql_connection
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_database_tables():
    """创建数据库表结构"""
    try:
        logger.info("🔧 开始创建数据库表结构...")
        
        # 读取SQL文件
        with open('database_extensions.sql', 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        success_count = 0
        for sql in sql_statements:
            try:
                cursor.execute(sql)
                success_count += 1
            except Exception as e:
                if "already exists" not in str(e).lower():
                    logger.warning(f"SQL执行警告: {e}")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info(f"✅ 数据库表结构创建完成，成功执行 {success_count} 条SQL语句")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建数据库表结构失败: {e}")
        return False

def initialize_digital_twins():
    """初始化设备数字孪生"""
    try:
        logger.info("🤖 开始初始化设备数字孪生...")
        
        digital_twin_service = DigitalTwinService()
        success_count = digital_twin_service.batch_initialize_twins()
        
        logger.info(f"✅ 设备数字孪生初始化完成，成功初始化 {success_count} 台设备")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"❌ 初始化设备数字孪生失败: {e}")
        return False

def start_data_collection():
    """启动数据采集服务"""
    try:
        logger.info("📊 启动数据采集服务...")
        
        data_collection_service = DataCollectionService()
        data_collection_service.start_collection()
        
        logger.info("✅ 数据采集服务启动成功")
        return data_collection_service
        
    except Exception as e:
        logger.error(f"❌ 启动数据采集服务失败: {e}")
        return None

def test_digital_twin_functionality():
    """测试数字孪生功能"""
    try:
        logger.info("🧪 开始测试数字孪生功能...")
        
        digital_twin_service = DigitalTwinService()
        
        # 获取一台设备进行测试
        conn = get_mysql_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT HANDLER_ID FROM EQP_STATUS WHERE STATUS = 'IDLE' LIMIT 1")
        result = cursor.fetchone()
        cursor.close()
        conn.close()
        
        if not result:
            logger.warning("⚠️ 没有找到可用的测试设备")
            return False
        
        equipment_id = result['HANDLER_ID']
        logger.info(f"📱 使用设备 {equipment_id} 进行测试")
        
        # 模拟传感器数据更新
        sensor_data = digital_twin_service.simulate_sensor_data(equipment_id)
        logger.info(f"📊 模拟传感器数据: {sensor_data}")
        
        # 更新设备状态
        state = digital_twin_service.update_equipment_state(equipment_id, sensor_data)
        logger.info(f"🔄 设备状态更新成功: 健康评分={state.health_score:.3f}, 效率={state.current_efficiency:.3f}")
        
        # 获取设备状态
        status = digital_twin_service.get_equipment_twin_status(equipment_id)
        if status:
            logger.info(f"📋 设备状态查询成功: {status}")
        
        logger.info("✅ 数字孪生功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数字孪生功能测试失败: {e}")
        return False

def generate_sample_data():
    """生成示例数据"""
    try:
        logger.info("📝 生成示例数据...")
        
        data_collection_service = DataCollectionService()
        
        # 模拟一些设备切换历史
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # 获取一些批次和设备
        cursor.execute("SELECT LOT_ID FROM ET_WAIT_LOT LIMIT 5")
        lots = cursor.fetchall()
        
        cursor.execute("SELECT HANDLER_ID FROM EQP_STATUS WHERE STATUS = 'IDLE' LIMIT 3")
        equipment = cursor.fetchall()
        
        if lots and equipment:
            import random
            for i in range(10):  # 生成10条切换记录
                eq_id = random.choice(equipment)['HANDLER_ID']
                from_lot = random.choice(lots)['LOT_ID']
                to_lot = random.choice(lots)['LOT_ID']
                switch_type = random.choice(['samesetup', 'smallchange', 'bigchange'])
                actual_time = random.randint(5, 30)
                
                data_collection_service.collect_switch_history(
                    eq_id, from_lot, to_lot, switch_type, actual_time
                )
        
        cursor.close()
        conn.close()
        
        logger.info("✅ 示例数据生成完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 生成示例数据失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始初始化数字孪生系统...")
    
    # 创建Flask应用上下文
    app_result = create_app()
    if isinstance(app_result, tuple):
        app = app_result[0]
    else:
        app = app_result
    
    with app.app_context():
        # 步骤1: 创建数据库表结构
        if not create_database_tables():
            logger.error("❌ 数据库表结构创建失败，终止初始化")
            return False
        
        # 步骤2: 初始化设备数字孪生
        if not initialize_digital_twins():
            logger.error("❌ 设备数字孪生初始化失败，终止初始化")
            return False
        
        # 步骤3: 测试数字孪生功能
        if not test_digital_twin_functionality():
            logger.error("❌ 数字孪生功能测试失败")
            return False
        
        # 步骤4: 生成示例数据
        if not generate_sample_data():
            logger.warning("⚠️ 示例数据生成失败，但不影响系统运行")
        
        # 步骤5: 启动数据采集服务
        data_service = start_data_collection()
        if not data_service:
            logger.warning("⚠️ 数据采集服务启动失败，但不影响系统运行")
        
        logger.info("🎉 数字孪生系统初始化完成！")
        logger.info("📊 系统功能:")
        logger.info("   - ✅ 设备数字孪生状态监控")
        logger.info("   - ✅ 实时健康评估")
        logger.info("   - ✅ 设备能力预测")
        logger.info("   - ✅ 数据采集和存储")
        logger.info("   - ✅ 切换历史记录")
        
        # 保持数据采集服务运行
        if data_service:
            logger.info("🔄 数据采集服务正在后台运行...")
            logger.info("💡 提示: 可以通过API查看设备数字孪生状态")
            logger.info("🌐 访问: http://127.0.0.1:5000/api/v2/equipment/digital-twin/status")
        
        return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n" + "="*60)
            print("🎉 数字孪生系统初始化成功！")
            print("📋 下一步可以:")
            print("   1. 运行 python run.py 启动Web服务")
            print("   2. 访问排产页面测试新功能")
            print("   3. 查看设备数字孪生状态")
            print("   4. 开始第二阶段：机器学习算法开发")
            print("="*60)
        else:
            print("\n❌ 数字孪生系统初始化失败，请检查日志")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断初始化")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 初始化过程中发生异常: {e}")
        sys.exit(1)
