#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的数据库表创建脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.utils.db_helper import get_mysql_connection
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_tables():
    """创建必要的数据库表"""
    
    # 创建Flask应用上下文
    app_result = create_app()
    if isinstance(app_result, tuple):
        app = app_result[0]
    else:
        app = app_result
    
    with app.app_context():
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 1. 创建设备数字孪生表
            logger.info("创建设备数字孪生表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS equipment_digital_twin (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    equipment_id VARCHAR(50) NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    temperature DECIMAL(5,2),
                    humidity DECIMAL(5,2),
                    vibration_level DECIMAL(8,4),
                    power_consumption DECIMAL(10,2),
                    cycle_count INT DEFAULT 0,
                    health_score DECIMAL(3,2) DEFAULT 1.00,
                    predicted_failure_time TIMESTAMP NULL,
                    maintenance_urgency ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'LOW',
                    current_efficiency DECIMAL(3,2) DEFAULT 1.00,
                    estimated_uph INT,
                    quality_index DECIMAL(3,2) DEFAULT 1.00,
                    is_active BOOLEAN DEFAULT TRUE,
                    last_maintenance TIMESTAMP NULL,
                    INDEX idx_equipment_time (equipment_id, timestamp),
                    INDEX idx_health_score (health_score)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备数字孪生实时状态表'
            """)
            
            # 2. 创建设备切换历史表
            logger.info("创建设备切换历史表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS equipment_switch_history (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    equipment_id VARCHAR(50) NOT NULL,
                    switch_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    from_lot_id VARCHAR(50),
                    to_lot_id VARCHAR(50),
                    from_product VARCHAR(100),
                    to_product VARCHAR(100),
                    from_stage VARCHAR(50),
                    to_stage VARCHAR(50),
                    switch_type ENUM('samesetup', 'smallchange', 'bigchange', 'major_change') NOT NULL,
                    predicted_switch_time INT,
                    actual_switch_time INT,
                    from_temperature DECIMAL(5,2),
                    to_temperature DECIMAL(5,2),
                    temperature_change_time INT,
                    tool_change_required BOOLEAN DEFAULT FALSE,
                    calibration_required BOOLEAN DEFAULT FALSE,
                    recipe_change_required BOOLEAN DEFAULT FALSE,
                    first_pass_yield DECIMAL(5,2),
                    ramp_up_time INT,
                    INDEX idx_equipment_switch (equipment_id, switch_timestamp),
                    INDEX idx_switch_type (switch_type),
                    INDEX idx_products (from_product, to_product)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备切换历史记录表'
            """)
            
            # 3. 创建排产决策历史表
            logger.info("创建排产决策历史表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS scheduling_decision_history (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    decision_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    algorithm_version VARCHAR(50),
                    decision_type ENUM('initial', 'reschedule', 'emergency') DEFAULT 'initial',
                    trigger_event VARCHAR(200),
                    total_lots INT,
                    available_equipment INT,
                    system_load_factor DECIMAL(3,2),
                    scheduled_lots INT,
                    average_priority_score DECIMAL(8,4),
                    estimated_makespan INT,
                    estimated_efficiency DECIMAL(3,2),
                    actual_makespan INT NULL,
                    actual_efficiency DECIMAL(3,2) NULL,
                    actual_yield DECIMAL(5,2) NULL,
                    decision_quality_score DECIMAL(3,2) NULL,
                    lessons_learned TEXT,
                    INDEX idx_decision_time (decision_timestamp),
                    INDEX idx_algorithm (algorithm_version),
                    INDEX idx_quality (decision_quality_score)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排产决策历史表'
            """)
            
            # 4. 创建产品工艺特征表
            logger.info("创建产品工艺特征表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS product_process_features (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    product_name VARCHAR(100) NOT NULL,
                    stage VARCHAR(50) NOT NULL,
                    standard_temperature DECIMAL(5,2),
                    temperature_tolerance DECIMAL(3,2),
                    standard_test_time INT,
                    complexity_level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM',
                    target_yield DECIMAL(5,2),
                    yield_sensitivity DECIMAL(3,2),
                    critical_parameters TEXT,
                    setup_complexity ENUM('SIMPLE', 'MODERATE', 'COMPLEX', 'VERY_COMPLEX') DEFAULT 'MODERATE',
                    preferred_equipment TEXT,
                    incompatible_equipment TEXT,
                    min_batch_size INT DEFAULT 1,
                    max_batch_size INT DEFAULT 999999,
                    processing_priority ENUM('LOW', 'NORMAL', 'HIGH', 'URGENT') DEFAULT 'NORMAL',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY uk_product_stage (product_name, stage),
                    INDEX idx_complexity (complexity_level),
                    INDEX idx_priority (processing_priority)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品工艺特征表'
            """)
            
            # 5. 创建实时事件日志表
            logger.info("创建实时事件日志表...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS real_time_events (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,
                    event_timestamp TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3),
                    event_type ENUM('equipment_status', 'lot_status', 'quality_alert', 'schedule_change', 'system_alert') NOT NULL,
                    event_source VARCHAR(100),
                    event_severity ENUM('INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO',
                    equipment_id VARCHAR(50),
                    lot_id VARCHAR(50),
                    event_data JSON,
                    event_message TEXT,
                    is_processed BOOLEAN DEFAULT FALSE,
                    processed_by VARCHAR(100),
                    processed_at TIMESTAMP NULL,
                    processing_result TEXT,
                    INDEX idx_event_time (event_timestamp),
                    INDEX idx_event_type (event_type),
                    INDEX idx_equipment (equipment_id),
                    INDEX idx_processed (is_processed)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时事件日志表'
            """)
            
            # 6. 插入初始产品特征数据
            logger.info("插入初始产品特征数据...")
            cursor.execute("""
                INSERT IGNORE INTO product_process_features 
                (product_name, stage, standard_temperature, temperature_tolerance, standard_test_time, complexity_level, target_yield, setup_complexity) 
                VALUES
                ('JWQ87820ESOP-SJA1_TR1', 'HOT-FT', 125.0, 5.0, 180, 'MEDIUM', 98.5, 'MODERATE'),
                ('JWQ85213-C244QFNA-SJA1_TR1', 'HOT-FT', 125.0, 5.0, 200, 'HIGH', 97.8, 'COMPLEX'),
                ('JWQ52992UDHBA_TR1', 'HOT-FT', 125.0, 5.0, 160, 'MEDIUM', 98.2, 'MODERATE'),
                ('JWQ5217DFND-M001_TR1', 'HOT-FT', 125.0, 5.0, 190, 'HIGH', 97.5, 'COMPLEX'),
                ('JWQ7101SOTB-J115_TR1', 'HOT-FT', 125.0, 5.0, 170, 'MEDIUM', 98.0, 'MODERATE'),
                ('JWH7030QFNAZ', 'BAKING2', 150.0, 5.0, 300, 'LOW', 99.2, 'SIMPLE'),
                ('JWQ950133-50ESOP_TR1', 'BAKING2', 150.0, 5.0, 280, 'LOW', 99.0, 'SIMPLE')
            """)
            
            # 7. 创建设备实时状态视图
            logger.info("创建设备实时状态视图...")
            cursor.execute("""
                CREATE OR REPLACE VIEW v_equipment_real_time_status AS
                SELECT 
                    e.HANDLER_ID,
                    e.STATUS as current_status,
                    e.DEVICE,
                    e.STAGE,
                    COALESCE(dt.health_score, 1.0) as health_score,
                    COALESCE(dt.current_efficiency, 1.0) as current_efficiency,
                    COALESCE(dt.estimated_uph, 1000) as estimated_uph,
                    COALESCE(dt.temperature, 25.0) as temperature,
                    COALESCE(dt.maintenance_urgency, 'LOW') as maintenance_urgency,
                    dt.timestamp as last_update,
                    CASE 
                        WHEN COALESCE(dt.health_score, 1.0) >= 0.9 AND e.STATUS = 'IDLE' THEN 'EXCELLENT'
                        WHEN COALESCE(dt.health_score, 1.0) >= 0.8 AND e.STATUS = 'IDLE' THEN 'GOOD'
                        WHEN COALESCE(dt.health_score, 1.0) >= 0.7 THEN 'FAIR'
                        WHEN COALESCE(dt.health_score, 1.0) >= 0.5 THEN 'POOR'
                        ELSE 'CRITICAL'
                    END as overall_condition
                FROM eqp_status e
                LEFT JOIN (
                    SELECT equipment_id, health_score, current_efficiency, estimated_uph, 
                           temperature, maintenance_urgency, timestamp,
                           ROW_NUMBER() OVER (PARTITION BY equipment_id ORDER BY timestamp DESC) as rn
                    FROM equipment_digital_twin 
                    WHERE is_active = TRUE
                ) dt ON e.HANDLER_ID = dt.equipment_id AND dt.rn = 1
            """)
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info("✅ 所有数据库表创建成功！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 创建数据库表失败: {e}")
            return False

if __name__ == "__main__":
    if create_tables():
        print("🎉 数据库表创建成功！现在可以运行数字孪生初始化脚本了。")
    else:
        print("❌ 数据库表创建失败！")
        sys.exit(1)
