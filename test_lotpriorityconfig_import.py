#!/usr/bin/env python3
"""
测试更新后的批次优先级配置导入功能
验证新增的lot_id字段是否正常工作
"""

import requests
import os
import pandas as pd
from datetime import datetime

# 服务器配置
SERVER_URL = "http://127.0.0.1:5000"
LOGIN_URL = f"{SERVER_URL}/auth/login"
UPLOAD_URL = f"{SERVER_URL}/api/v2/production/priority-settings/upload"
LOT_CONFIG_URL = f"{SERVER_URL}/api/v2/production/priority/lot"

def check_excel_structure():
    """检查Excel文件结构"""
    print("📋 检查Excel文件结构...")
    
    excel_file = "Excellist2025.06.05/lotpriorityconfig.xlsx"
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        df = pd.read_excel(excel_file)
        print(f"✅ Excel文件读取成功，共 {len(df)} 行数据")
        print(f"📋 字段列表: {list(df.columns)}")
        print(f"📋 数据样例:")
        print(df.head(3).to_string())
        
        # 检查必需字段
        required_fields = ['lot_id', 'device', 'priority']
        missing_fields = [field for field in required_fields if field not in df.columns]
        
        if missing_fields:
            print(f"❌ 缺少必需字段: {missing_fields}")
            return False
        
        print(f"✅ 所有必需字段都存在: {required_fields}")
        return True
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
        return False

def login_and_get_session():
    """登录并获取会话"""
    session = requests.Session()
    
    # 执行登录
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        response = session.post(LOGIN_URL, data=login_data)
        print(f"🔐 登录响应: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return None

def test_upload(session):
    """测试上传功能"""
    print("\n📤 测试批次优先级配置上传...")
    
    excel_file = "Excellist2025.06.05/lotpriorityconfig.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ 测试文件不存在: {excel_file}")
        return False
    
    try:
        with open(excel_file, 'rb') as f:
            files = {'files': ('lotpriorityconfig.xlsx', f, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')}
            response = session.post(UPLOAD_URL, files=files)
        
        print(f"📤 上传响应: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 上传成功: {result}")
            
            if result.get('success'):
                print(f"✅ 导入成功，处理了 {result.get('total_processed', 0)} 条记录")
                return True
            else:
                print(f"❌ 导入失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 上传失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 上传请求失败: {e}")
        return False

def test_query(session):
    """测试查询功能"""
    print("\n📊 测试批次优先级配置查询...")
    
    try:
        response = session.get(LOT_CONFIG_URL)
        print(f"📊 查询响应: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 查询成功")
            print(f"📊 总记录数: {result.get('total', 0)}")
            print(f"📊 当前页记录数: {len(result.get('items', []))}")
            
            # 显示前几条记录
            items = result.get('items', [])
            if items:
                print("\n📋 查询结果样例:")
                for i, item in enumerate(items[:3]):
                    print(f"  记录 {i+1}:")
                    print(f"    lot_id: {item.get('lot_id')}")
                    print(f"    device: {item.get('device')}")
                    print(f"    stage: {item.get('stage')}")
                    print(f"    priority: {item.get('priority')}")
                    print(f"    refresh_time: {item.get('refresh_time')}")
                    print(f"    user: {item.get('user')}")
                    print()
            else:
                print("⚠️  查询结果为空")
            
            return True
        else:
            print(f"❌ 查询失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 查询请求失败: {e}")
        return False

def test_search_with_filters(session):
    """测试带过滤条件的查询"""
    print("\n🔍 测试过滤查询...")
    
    # 测试不同的过滤条件
    test_cases = [
        {'lot_id': 'YX25'},
        {'device': 'JWQ5217'},
        {'stage': 'room'},
        {'priority': 'n'}
    ]
    
    for i, params in enumerate(test_cases):
        print(f"\n  测试 {i+1}: {params}")
        try:
            response = session.get(LOT_CONFIG_URL, params=params)
            if response.status_code == 200:
                result = response.json()
                print(f"    ✅ 查询成功，找到 {result.get('total', 0)} 条记录")
            else:
                print(f"    ❌ 查询失败: {response.status_code}")
        except Exception as e:
            print(f"    ❌ 查询异常: {e}")

def main():
    """主函数"""
    print("🚀 开始测试更新后的批次优先级配置功能...")
    print("=" * 60)
    
    # 检查Excel文件结构
    if not check_excel_structure():
        print("❌ Excel文件结构检查失败，终止测试")
        return
    
    # 登录
    session = login_and_get_session()
    if not session:
        print("❌ 登录失败，终止测试")
        return
    
    # 测试上传
    if not test_upload(session):
        print("❌ 上传测试失败，终止后续测试")
        return
    
    # 测试查询
    if not test_query(session):
        print("❌ 查询测试失败")
        return
    
    # 测试过滤查询
    test_search_with_filters(session)
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成！")
    print("✅ 批次优先级配置功能更新成功，包含以下新特性:")
    print("   - 新增了 lot_id 字段")
    print("   - 支持按 lot_id、device、stage、priority 过滤查询")
    print("   - Excel导入功能正常工作")
    print("   - API查询接口正常工作")

if __name__ == "__main__":
    main() 