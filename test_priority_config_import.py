#!/usr/bin/env python3
"""
测试优先级配置Excel导入功能
验证修复后的完整导入链条
"""

import requests
import os
import pandas as pd
from datetime import datetime

# 服务器配置
SERVER_URL = "http://127.0.0.1:5000"
LOGIN_URL = f"{SERVER_URL}/auth/login"
UPLOAD_URL = f"{SERVER_URL}/api/v2/production/priority-settings/upload"

def login_and_get_session():
    """登录并获取会话"""
    session = requests.Session()
    
    # 获取登录页面（可能需要CSRF token）
    try:
        response = session.get(LOGIN_URL)
        print(f"📋 访问登录页面: {response.status_code}")
    except Exception as e:
        print(f"❌ 访问登录页面失败: {e}")
        return None
    
    # 执行登录
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        response = session.post(LOGIN_URL, data=login_data)
        print(f"🔐 登录响应: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ 登录成功")
            return session
        else:
            print(f"❌ 登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return None

def test_excel_upload(session, excel_file):
    """测试Excel文件上传"""
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return False
    
    try:
        # 准备文件上传
        files = {
            'files': (os.path.basename(excel_file), open(excel_file, 'rb'), 
                     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        }
        
        print(f"📤 上传文件: {excel_file}")
        response = session.post(UPLOAD_URL, files=files)
        
        # 关闭文件
        files['files'][1].close()
        
        print(f"📊 上传响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 上传结果: {result}")
            
            if result.get('success'):
                total_processed = result.get('total_processed', 0)
                print(f"✅ 上传成功! 处理了 {total_processed} 条记录")
                return True
            else:
                print(f"❌ 上传失败: {result.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 上传请求失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return False

def check_excel_structure(excel_file):
    """检查Excel文件结构"""
    try:
        df = pd.read_excel(excel_file)
        print(f"📊 {os.path.basename(excel_file)} 结构:")
        print(f"  行数: {len(df)}")
        print(f"  列数: {len(df.columns)}")
        print(f"  字段: {list(df.columns)}")
        print(f"  前3行数据:")
        print(df.head(3).to_string(index=False))
        print()
        return True
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试优先级配置Excel导入功能...")
    print("=" * 80)
    
    # 1. 检查Excel文件结构
    print("1️⃣ 检查Excel文件结构...")
    excel_files = [
        "Excellist2025.06.05/devicepriorityconfig.xlsx",
        "Excellist2025.06.05/lotpriorityconfig.xlsx"
    ]
    
    for excel_file in excel_files:
        if os.path.exists(excel_file):
            check_excel_structure(excel_file)
        else:
            print(f"❌ 文件不存在: {excel_file}")
    
    # 2. 登录系统
    print("2️⃣ 登录系统...")
    session = login_and_get_session()
    if not session:
        print("❌ 登录失败，无法继续测试")
        return
    
    # 3. 测试文件上传
    print("3️⃣ 测试Excel文件上传...")
    success_count = 0
    
    for excel_file in excel_files:
        if os.path.exists(excel_file):
            print(f"\n📤 测试上传: {excel_file}")
            if test_excel_upload(session, excel_file):
                success_count += 1
            print("-" * 60)
        else:
            print(f"⏭️  跳过不存在的文件: {excel_file}")
    
    # 4. 总结
    print("📋 测试结果总结:")
    print(f"  总文件数: {len([f for f in excel_files if os.path.exists(f)])}")
    print(f"  成功上传: {success_count}")
    print(f"  测试状态: {'✅ 全部成功' if success_count == len([f for f in excel_files if os.path.exists(f)]) else '❌ 部分失败'}")
    
    if success_count > 0:
        print("\n💡 建议验证:")
        print("  1. 登录Web界面查看数据是否正确显示")
        print("  2. 检查数据库表中的记录数")
        print("  3. 验证字段映射是否正确")

if __name__ == "__main__":
    main() 