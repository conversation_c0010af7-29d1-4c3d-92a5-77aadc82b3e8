#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据采集服务
负责收集设备运行数据、排产执行数据和质量数据
为AI算法提供训练数据
"""

import logging
import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from app.utils.db_helper import get_mysql_connection
from app.services.digital_twin_service import DigitalTwinService

logger = logging.getLogger(__name__)

class DataCollectionService:
    """数据采集服务"""
    
    def __init__(self):
        self.digital_twin_service = DigitalTwinService()
        self.collection_interval = 60  # 采集间隔(秒)
        self.is_running = False
        self.collection_thread = None
    
    def start_collection(self):
        """启动数据采集"""
        if self.is_running:
            logger.warning("数据采集已在运行中")
            return
        
        self.is_running = True
        self.collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self.collection_thread.start()
        logger.info("✅ 数据采集服务启动成功")
    
    def stop_collection(self):
        """停止数据采集"""
        self.is_running = False
        if self.collection_thread:
            self.collection_thread.join(timeout=5)
        logger.info("⏹️ 数据采集服务已停止")
    
    def _collection_loop(self):
        """数据采集主循环"""
        while self.is_running:
            try:
                # 采集设备状态数据
                self._collect_equipment_data()
                
                # 采集排产执行数据
                self._collect_scheduling_data()
                
                # 采集质量数据
                self._collect_quality_data()
                
                # 等待下次采集
                time.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"❌ 数据采集循环异常: {e}")
                time.sleep(10)  # 异常时短暂等待
    
    def _collect_equipment_data(self):
        """采集设备数据"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 获取所有活跃设备
            cursor.execute("""
                SELECT HANDLER_ID FROM EQP_STATUS 
                WHERE STATUS IN ('IDLE', 'Run', 'SetupRun')
            """)
            
            equipment_list = cursor.fetchall()
            cursor.close()
            conn.close()
            
            for equipment in equipment_list:
                equipment_id = equipment['HANDLER_ID']
                
                # 模拟获取传感器数据
                sensor_data = self.digital_twin_service.simulate_sensor_data(equipment_id)
                
                # 更新数字孪生状态
                self.digital_twin_service.update_equipment_state(equipment_id, sensor_data)
            
            logger.debug(f"📊 采集了 {len(equipment_list)} 台设备的数据")
            
        except Exception as e:
            logger.error(f"❌ 采集设备数据失败: {e}")
    
    def _collect_scheduling_data(self):
        """采集排产执行数据"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 检查是否有新的排产记录
            cursor.execute("""
                SELECT COUNT(*) as new_records FROM lotprioritydone 
                WHERE CREATE_TIME >= %s
            """, (datetime.now() - timedelta(minutes=self.collection_interval/60),))
            
            result = cursor.fetchone()
            new_records = result['new_records'] if result else 0
            
            if new_records > 0:
                # 记录排产决策
                self._record_scheduling_decision(new_records)
                logger.debug(f"📋 记录了 {new_records} 条新排产决策")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ 采集排产数据失败: {e}")
    
    def _collect_quality_data(self):
        """采集质量数据"""
        try:
            # 这里可以集成实际的质量数据源
            # 目前模拟质量数据采集
            
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 模拟质量事件
            import random
            if random.random() < 0.1:  # 10%概率产生质量事件
                self._generate_quality_event()
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ 采集质量数据失败: {e}")
    
    def _record_scheduling_decision(self, scheduled_lots: int):
        """记录排产决策"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 获取当前系统状态
            cursor.execute("SELECT COUNT(*) as total_equipment FROM EQP_STATUS")
            equipment_count = cursor.fetchone()['total_equipment']
            
            cursor.execute("SELECT COUNT(*) as waiting_lots FROM ET_WAIT_LOT WHERE WIP_STATE = 'Released'")
            waiting_lots = cursor.fetchone()['waiting_lots']
            
            # 记录决策历史
            decision_data = {
                'algorithm_version': 'v1.0',
                'decision_type': 'initial',
                'total_lots': waiting_lots,
                'available_equipment': equipment_count,
                'system_load_factor': min(waiting_lots / max(equipment_count, 1), 2.0),
                'scheduled_lots': scheduled_lots,
                'estimated_makespan': scheduled_lots * 2,  # 估算
                'estimated_efficiency': 0.85
            }
            
            cursor.execute("""
                INSERT INTO scheduling_decision_history 
                (algorithm_version, decision_type, total_lots, available_equipment, 
                 system_load_factor, scheduled_lots, estimated_makespan, estimated_efficiency)
                VALUES (%(algorithm_version)s, %(decision_type)s, %(total_lots)s, 
                        %(available_equipment)s, %(system_load_factor)s, %(scheduled_lots)s,
                        %(estimated_makespan)s, %(estimated_efficiency)s)
            """, decision_data)
            
            conn.commit()
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ 记录排产决策失败: {e}")
    
    def _generate_quality_event(self):
        """生成质量事件（模拟）"""
        try:
            import random
            
            event_types = ['quality_alert', 'equipment_status', 'lot_status']
            severities = ['INFO', 'WARNING', 'ERROR']
            
            event_data = {
                'event_type': random.choice(event_types),
                'event_severity': random.choice(severities),
                'event_source': 'quality_system',
                'event_data': json.dumps({
                    'yield': round(random.uniform(95, 99.5), 2),
                    'defect_type': random.choice(['electrical', 'mechanical', 'thermal']),
                    'batch_id': f'BATCH_{random.randint(1000, 9999)}'
                }),
                'event_message': '模拟质量事件',
                'is_processed': False
            }
            
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO real_time_events 
                (event_type, event_severity, event_source, event_data, event_message, is_processed)
                VALUES (%(event_type)s, %(event_severity)s, %(event_source)s, 
                        %(event_data)s, %(event_message)s, %(is_processed)s)
            """, event_data)
            
            conn.commit()
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ 生成质量事件失败: {e}")
    
    def collect_switch_history(self, equipment_id: str, from_lot: str, to_lot: str, 
                              switch_type: str, actual_time: int):
        """收集设备切换历史数据"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 获取批次信息
            cursor.execute("""
                SELECT DEVICE, STAGE FROM ET_WAIT_LOT WHERE LOT_ID = %s
            """, (from_lot,))
            from_info = cursor.fetchone()
            
            cursor.execute("""
                SELECT DEVICE, STAGE FROM ET_WAIT_LOT WHERE LOT_ID = %s
            """, (to_lot,))
            to_info = cursor.fetchone()
            
            # 记录切换历史
            switch_data = {
                'equipment_id': equipment_id,
                'from_lot_id': from_lot,
                'to_lot_id': to_lot,
                'from_product': from_info['DEVICE'] if from_info else '',
                'to_product': to_info['DEVICE'] if to_info else '',
                'from_stage': from_info['STAGE'] if from_info else '',
                'to_stage': to_info['STAGE'] if to_info else '',
                'switch_type': switch_type,
                'actual_switch_time': actual_time,
                'from_temperature': 25.0,  # 可以从数字孪生获取
                'to_temperature': 125.0,   # 可以从产品特征获取
                'temperature_change_time': max(0, actual_time - 10),
                'tool_change_required': switch_type in ['bigchange', 'major_change'],
                'calibration_required': switch_type != 'samesetup',
                'recipe_change_required': switch_type != 'samesetup'
            }
            
            cursor.execute("""
                INSERT INTO equipment_switch_history 
                (equipment_id, from_lot_id, to_lot_id, from_product, to_product,
                 from_stage, to_stage, switch_type, actual_switch_time,
                 from_temperature, to_temperature, temperature_change_time,
                 tool_change_required, calibration_required, recipe_change_required)
                VALUES (%(equipment_id)s, %(from_lot_id)s, %(to_lot_id)s, %(from_product)s,
                        %(to_product)s, %(from_stage)s, %(to_stage)s, %(switch_type)s,
                        %(actual_switch_time)s, %(from_temperature)s, %(to_temperature)s,
                        %(temperature_change_time)s, %(tool_change_required)s,
                        %(calibration_required)s, %(recipe_change_required)s)
            """, switch_data)
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info(f"✅ 记录设备 {equipment_id} 切换历史: {from_lot} -> {to_lot}")
            
        except Exception as e:
            logger.error(f"❌ 记录切换历史失败: {e}")
    
    def get_training_data(self, data_type: str, limit: int = 1000) -> List[Dict]:
        """获取训练数据"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            if data_type == 'switch_cost':
                cursor.execute("""
                    SELECT * FROM equipment_switch_history 
                    WHERE actual_switch_time IS NOT NULL
                    ORDER BY switch_timestamp DESC
                    LIMIT %s
                """, (limit,))
            
            elif data_type == 'equipment_health':
                cursor.execute("""
                    SELECT * FROM equipment_digital_twin 
                    WHERE health_score IS NOT NULL
                    ORDER BY timestamp DESC
                    LIMIT %s
                """, (limit,))
            
            elif data_type == 'scheduling_performance':
                cursor.execute("""
                    SELECT * FROM scheduling_decision_history 
                    WHERE actual_makespan IS NOT NULL
                    ORDER BY decision_timestamp DESC
                    LIMIT %s
                """, (limit,))
            
            else:
                logger.error(f"未知的数据类型: {data_type}")
                return []
            
            results = cursor.fetchall()
            cursor.close()
            conn.close()
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 获取训练数据失败: {e}")
            return []
