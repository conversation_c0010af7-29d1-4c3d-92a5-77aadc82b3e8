#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备数字孪生服务
实现设备的实时状态监控、健康评估和能力预测
"""

import logging
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from app.utils.db_helper import get_mysql_connection

logger = logging.getLogger(__name__)

@dataclass
class EquipmentState:
    """设备状态数据类"""
    equipment_id: str
    timestamp: datetime
    temperature: float
    humidity: float
    vibration_level: float
    power_consumption: float
    cycle_count: int
    health_score: float
    current_efficiency: float
    estimated_uph: int
    quality_index: float

@dataclass
class HealthAssessment:
    """健康评估结果"""
    health_score: float
    predicted_failure_time: Optional[datetime]
    maintenance_urgency: str
    risk_factors: List[str]
    recommendations: List[str]

class DigitalTwinService:
    """设备数字孪生服务"""
    
    def __init__(self):
        self.equipment_twins = {}
        self.health_thresholds = {
            'temperature_max': 130.0,
            'vibration_max': 0.5,
            'efficiency_min': 0.8,
            'cycle_count_warning': 10000
        }
    
    def initialize_equipment_twin(self, equipment_id: str) -> bool:
        """初始化设备数字孪生"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 获取设备基础信息
            cursor.execute("""
                SELECT HANDLER_ID, DEVICE, STAGE, STATUS, TEMPERATURE_CAPACITY
                FROM EQP_STATUS 
                WHERE HANDLER_ID = %s
            """, (equipment_id,))
            
            equipment_info = cursor.fetchone()
            if not equipment_info:
                logger.error(f"设备 {equipment_id} 不存在")
                return False
            
            # 创建初始数字孪生记录
            initial_state = {
                'equipment_id': equipment_id,
                'temperature': 25.0,  # 室温
                'humidity': 45.0,
                'vibration_level': 0.1,
                'power_consumption': 0.0,
                'cycle_count': 0,
                'health_score': 1.0,
                'current_efficiency': 1.0,
                'estimated_uph': 1000,
                'quality_index': 1.0,
                'is_active': True
            }
            
            cursor.execute("""
                INSERT INTO equipment_digital_twin 
                (equipment_id, temperature, humidity, vibration_level, power_consumption,
                 cycle_count, health_score, current_efficiency, estimated_uph, quality_index, is_active)
                VALUES (%(equipment_id)s, %(temperature)s, %(humidity)s, %(vibration_level)s,
                        %(power_consumption)s, %(cycle_count)s, %(health_score)s,
                        %(current_efficiency)s, %(estimated_uph)s, %(quality_index)s, %(is_active)s)
                ON DUPLICATE KEY UPDATE
                is_active = VALUES(is_active)
            """, initial_state)
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info(f"✅ 设备 {equipment_id} 数字孪生初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 设备 {equipment_id} 数字孪生初始化失败: {e}")
            return False
    
    def update_equipment_state(self, equipment_id: str, sensor_data: Dict) -> EquipmentState:
        """更新设备状态"""
        try:
            # 模拟传感器数据处理
            processed_data = self._process_sensor_data(sensor_data)
            
            # 计算健康评分
            health_assessment = self._assess_equipment_health(equipment_id, processed_data)
            
            # 预测设备能力
            capability = self._predict_equipment_capability(equipment_id, processed_data, health_assessment)
            
            # 更新数据库
            state = EquipmentState(
                equipment_id=equipment_id,
                timestamp=datetime.now(),
                temperature=processed_data.get('temperature', 25.0),
                humidity=processed_data.get('humidity', 45.0),
                vibration_level=processed_data.get('vibration_level', 0.1),
                power_consumption=processed_data.get('power_consumption', 0.0),
                cycle_count=processed_data.get('cycle_count', 0),
                health_score=health_assessment.health_score,
                current_efficiency=capability['efficiency'],
                estimated_uph=capability['uph'],
                quality_index=capability['quality_index']
            )
            
            self._save_state_to_db(state, health_assessment)
            
            return state
            
        except Exception as e:
            logger.error(f"❌ 更新设备 {equipment_id} 状态失败: {e}")
            raise
    
    def _process_sensor_data(self, raw_data: Dict) -> Dict:
        """处理原始传感器数据"""
        # 模拟数据处理逻辑
        processed = {}
        
        # 温度数据处理
        if 'temperature' in raw_data:
            temp = float(raw_data['temperature'])
            # 异常值检测和平滑处理
            if 0 <= temp <= 200:
                processed['temperature'] = temp
            else:
                processed['temperature'] = 25.0  # 默认值
        
        # 湿度数据处理
        if 'humidity' in raw_data:
            humidity = float(raw_data['humidity'])
            processed['humidity'] = max(0, min(100, humidity))
        
        # 振动数据处理
        if 'vibration' in raw_data:
            vibration = float(raw_data['vibration'])
            processed['vibration_level'] = max(0, vibration)
        
        # 功耗数据处理
        if 'power' in raw_data:
            power = float(raw_data['power'])
            processed['power_consumption'] = max(0, power)
        
        # 循环计数
        if 'cycle_count' in raw_data:
            processed['cycle_count'] = int(raw_data['cycle_count'])
        
        return processed
    
    def _assess_equipment_health(self, equipment_id: str, data: Dict) -> HealthAssessment:
        """评估设备健康状态"""
        health_score = 1.0
        risk_factors = []
        recommendations = []
        
        # 温度健康评估
        temp = data.get('temperature', 25.0)
        if temp > self.health_thresholds['temperature_max']:
            health_score -= 0.3
            risk_factors.append(f"温度过高: {temp}°C")
            recommendations.append("检查冷却系统")
        elif temp > self.health_thresholds['temperature_max'] * 0.9:
            health_score -= 0.1
            risk_factors.append(f"温度偏高: {temp}°C")
        
        # 振动健康评估
        vibration = data.get('vibration_level', 0.1)
        if vibration > self.health_thresholds['vibration_max']:
            health_score -= 0.2
            risk_factors.append(f"振动异常: {vibration}")
            recommendations.append("检查机械部件")
        
        # 循环计数评估
        cycle_count = data.get('cycle_count', 0)
        if cycle_count > self.health_thresholds['cycle_count_warning']:
            health_score -= 0.1
            risk_factors.append(f"使用次数较高: {cycle_count}")
            recommendations.append("安排预防性维护")
        
        # 确保健康评分在合理范围内
        health_score = max(0.0, min(1.0, health_score))
        
        # 预测故障时间
        predicted_failure = None
        if health_score < 0.5:
            # 基于健康评分预测故障时间
            days_to_failure = int((health_score * 30) + 1)
            predicted_failure = datetime.now() + timedelta(days=days_to_failure)
        
        # 确定维护紧急程度
        if health_score >= 0.9:
            urgency = 'LOW'
        elif health_score >= 0.7:
            urgency = 'MEDIUM'
        elif health_score >= 0.5:
            urgency = 'HIGH'
        else:
            urgency = 'CRITICAL'
        
        return HealthAssessment(
            health_score=health_score,
            predicted_failure_time=predicted_failure,
            maintenance_urgency=urgency,
            risk_factors=risk_factors,
            recommendations=recommendations
        )
    
    def _predict_equipment_capability(self, equipment_id: str, data: Dict, health: HealthAssessment) -> Dict:
        """预测设备能力"""
        base_efficiency = 1.0
        base_uph = 1000
        base_quality = 1.0
        
        # 基于健康状态调整能力
        health_factor = health.health_score
        
        # 温度对效率的影响
        temp = data.get('temperature', 25.0)
        if 20 <= temp <= 30:  # 最佳温度范围
            temp_factor = 1.0
        elif temp > 100:  # 高温工作
            temp_factor = 0.95
        else:
            temp_factor = 0.98
        
        # 计算实际能力
        actual_efficiency = base_efficiency * health_factor * temp_factor
        actual_uph = int(base_uph * actual_efficiency)
        actual_quality = base_quality * health_factor
        
        return {
            'efficiency': round(actual_efficiency, 3),
            'uph': actual_uph,
            'quality_index': round(actual_quality, 3)
        }
    
    def _save_state_to_db(self, state: EquipmentState, health: HealthAssessment):
        """保存状态到数据库"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO equipment_digital_twin 
                (equipment_id, temperature, humidity, vibration_level, power_consumption,
                 cycle_count, health_score, predicted_failure_time, maintenance_urgency,
                 current_efficiency, estimated_uph, quality_index, is_active)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                state.equipment_id, state.temperature, state.humidity,
                state.vibration_level, state.power_consumption, state.cycle_count,
                state.health_score, health.predicted_failure_time, health.maintenance_urgency,
                state.current_efficiency, state.estimated_uph, state.quality_index, True
            ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
        except Exception as e:
            logger.error(f"❌ 保存设备状态失败: {e}")
            raise
    
    def get_equipment_twin_status(self, equipment_id: str) -> Optional[Dict]:
        """获取设备数字孪生状态"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM v_equipment_real_time_status 
                WHERE HANDLER_ID = %s
            """, (equipment_id,))
            
            result = cursor.fetchone()
            cursor.close()
            conn.close()
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 获取设备 {equipment_id} 状态失败: {e}")
            return None
    
    def simulate_sensor_data(self, equipment_id: str) -> Dict:
        """模拟传感器数据（用于测试）"""
        import random
        
        # 模拟真实的传感器数据
        return {
            'temperature': round(random.uniform(20, 130), 2),
            'humidity': round(random.uniform(30, 60), 1),
            'vibration': round(random.uniform(0.05, 0.3), 4),
            'power': round(random.uniform(50, 200), 2),
            'cycle_count': random.randint(0, 15000)
        }
    
    def batch_initialize_twins(self) -> int:
        """批量初始化所有设备的数字孪生"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 获取所有设备
            cursor.execute("SELECT HANDLER_ID FROM EQP_STATUS WHERE STATUS = 'IDLE'")
            equipment_list = cursor.fetchall()
            
            cursor.close()
            conn.close()
            
            success_count = 0
            for equipment in equipment_list:
                equipment_id = equipment['HANDLER_ID']
                if self.initialize_equipment_twin(equipment_id):
                    success_count += 1
            
            logger.info(f"✅ 批量初始化完成，成功: {success_count}/{len(equipment_list)}")
            return success_count
            
        except Exception as e:
            logger.error(f"❌ 批量初始化失败: {e}")
            return 0
