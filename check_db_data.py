#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库数据状态
"""

import pymysql
import sys

def check_database_data():
    """检查数据库中的关键数据"""
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='WWWwww123!',
            database='aps',
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        
        print("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            # 1. 检查ET_WAIT_LOT表数据
            print("\n=== ET_WAIT_LOT 表数据检查 ===")
            cursor.execute("""
                SELECT COUNT(*) as total_count FROM ET_WAIT_LOT
            """)
            total = cursor.fetchone()['total_count']
            print(f"总记录数: {total}")
            
            # 检查WIP_STATE和PROC_STATE的分布
            cursor.execute("""
                SELECT WIP_STATE, PROC_STATE, COUNT(*) as count 
                FROM ET_WAIT_LOT 
                GROUP BY WIP_STATE, PROC_STATE
                ORDER BY count DESC
            """)
            state_distribution = cursor.fetchall()
            print("\nWIP_STATE和PROC_STATE分布:")
            for row in state_distribution:
                print(f"  WIP_STATE: '{row['WIP_STATE']}', PROC_STATE: '{row['PROC_STATE']}', 数量: {row['count']}")
            
            # 检查符合排产条件的数据
            print("\n=== 符合排产条件的数据检查 ===")
            
            # 检查Released + wait状态的数据
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM ET_WAIT_LOT 
                WHERE WIP_STATE = 'Released' AND PROC_STATE = 'wait'
                  AND GOOD_QTY > 0
            """)
            released_wait = cursor.fetchone()['count']
            print(f"WIP_STATE='Released' AND PROC_STATE='wait' 的记录数: {released_wait}")
            
            # 检查当前排产服务使用的条件
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM ET_WAIT_LOT 
                WHERE WIP_STATE IN ('WAIT', 'READY')
                  AND HOLD_STATE = 0
                  AND GOOD_QTY > 0
            """)
            current_condition = cursor.fetchone()['count']
            print(f"当前排产条件 (WIP_STATE IN ('WAIT', 'READY') AND HOLD_STATE = 0) 的记录数: {current_condition}")
            
            # 显示前10条数据的详细信息
            print("\n=== 前10条数据详情 ===")
            cursor.execute("""
                SELECT LOT_ID, DEVICE, STAGE, WIP_STATE, PROC_STATE, HOLD_STATE, GOOD_QTY
                FROM ET_WAIT_LOT 
                WHERE GOOD_QTY > 0
                ORDER BY CREATE_TIME DESC
                LIMIT 10
            """)
            sample_data = cursor.fetchall()
            for i, row in enumerate(sample_data, 1):
                print(f"{i}. LOT_ID: {row['LOT_ID']}, DEVICE: {row['DEVICE']}, STAGE: {row['STAGE']}")
                print(f"   WIP_STATE: '{row['WIP_STATE']}', PROC_STATE: '{row['PROC_STATE']}', HOLD_STATE: {row['HOLD_STATE']}, GOOD_QTY: {row['GOOD_QTY']}")
            
            # 2. 检查EQP_STATUS表数据
            print("\n=== EQP_STATUS 表数据检查 ===")
            cursor.execute("""
                SELECT COUNT(*) as total_count FROM EQP_STATUS
            """)
            eqp_total = cursor.fetchone()['total_count']
            print(f"设备总数: {eqp_total}")
            
            cursor.execute("""
                SELECT STATUS, COUNT(*) as count 
                FROM EQP_STATUS 
                GROUP BY STATUS
                ORDER BY count DESC
            """)
            eqp_status_distribution = cursor.fetchall()
            print("\n设备状态分布:")
            for row in eqp_status_distribution:
                print(f"  STATUS: '{row['STATUS']}', 数量: {row['count']}")
            
            # 检查可用设备
            cursor.execute("""
                SELECT COUNT(*) as count 
                FROM EQP_STATUS 
                WHERE STATUS IN ('0', 'IDLE')
            """)
            available_eqp = cursor.fetchone()['count']
            print(f"可用设备数量 (STATUS IN ('0', 'IDLE')): {available_eqp}")
            
            # 3. 检查其他关键表
            tables_to_check = ['ET_UPH_EQP', 'ET_FT_TEST_SPEC', 'ET_RECIPE_FILE']
            for table in tables_to_check:
                try:
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                    count = cursor.fetchone()['count']
                    print(f"\n{table} 表记录数: {count}")
                except Exception as e:
                    print(f"\n{table} 表检查失败: {e}")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    check_database_data()
