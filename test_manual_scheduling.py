#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的手动排产功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 创建Flask应用上下文
from app import create_app
from app.services.manual_scheduling_service import ManualSchedulingService
import logging

# 创建应用实例
app = create_app()

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_manual_scheduling():
    """测试手动排产功能"""
    with app.app_context():
        try:
            print("=== 测试手动排产功能 ===")

            # 创建排产服务实例
            service = ManualSchedulingService()
        
        # 测试不同的排产策略
        algorithms = ['intelligent', 'deadline', 'product', 'value']
        
        for algorithm in algorithms:
            print(f"\n--- 测试排产策略: {algorithm} ---")
            
            result = service.execute_manual_scheduling(
                algorithm=algorithm,
                optimization_target='balanced'
            )
            
            print(f"排产结果: {result['success']}")
            print(f"消息: {result['message']}")
            
            if result['success']:
                schedule = result.get('schedule', [])
                metrics = result.get('metrics', {})
                
                print(f"生成排产记录数: {len(schedule)}")
                print(f"待排产批次总数: {metrics.get('total_lots', 0)}")
                print(f"成功排产批次数: {metrics.get('scheduled_lots', 0)}")
                print(f"可用设备数: {metrics.get('available_equipment', 0)}")
                
                # 显示前5条排产记录
                if schedule:
                    print("\n前5条排产记录:")
                    for i, record in enumerate(schedule[:5], 1):
                        lot = record.get('lot', {})
                        equipment = record.get('equipment', {})
                        print(f"  {i}. LOT_ID: {lot.get('LOT_ID', 'N/A')}")
                        print(f"     DEVICE: {lot.get('DEVICE', 'N/A')}")
                        print(f"     STAGE: {lot.get('STAGE', 'N/A')}")
                        print(f"     HANDLER_ID: {equipment.get('HANDLER_ID', 'N/A')}")
                        print(f"     MATCH_TYPE: {equipment.get('MATCH_TYPE', 'N/A')}")
                        print(f"     PRIORITY_SCORE: {record.get('priority_score', 0):.2f}")
                        print()
            else:
                print(f"排产失败: {result.get('message', '未知错误')}")
            
            print("-" * 50)

            return True

        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_data_retrieval():
    """测试数据获取功能"""
    with app.app_context():
        try:
            print("\n=== 测试数据获取功能 ===")

            service = ManualSchedulingService()
        
        # 测试获取待排产批次
        print("1. 测试获取待排产批次...")
        lots = service._get_schedulable_lots()
        print(f"   获取到 {len(lots)} 条待排产批次")
        
        if lots:
            sample_lot = lots[0]
            print(f"   示例批次: {sample_lot.get('LOT_ID')} - {sample_lot.get('DEVICE')} - {sample_lot.get('STAGE')}")
            print(f"   状态: WIP_STATE={sample_lot.get('WIP_STATE')}, PROC_STATE={sample_lot.get('PROC_STATE')}")
        
        # 测试获取可用设备
        print("\n2. 测试获取可用设备...")
        equipment = service._get_available_equipment()
        print(f"   获取到 {len(equipment)} 台可用设备")
        
        if equipment:
            sample_eq = equipment[0]
            print(f"   示例设备: {sample_eq.get('HANDLER_ID')} - {sample_eq.get('DEVICE')} - {sample_eq.get('STAGE')}")
            print(f"   状态: STATUS={sample_eq.get('STATUS')}")
        
            # 测试获取UPH数据
            print("\n3. 测试获取UPH数据...")
            uph_data = service._get_uph_data()
            print(f"   获取到 {len(uph_data)} 条UPH数据")

            # 测试获取测试规范
            print("\n4. 测试获取测试规范...")
            test_specs = service._get_test_specs()
            print(f"   获取到 {len(test_specs)} 条测试规范")

            # 测试获取配方文件
            print("\n5. 测试获取配方文件...")
            recipe_files = service._get_recipe_files()
            print(f"   获取到 {len(recipe_files)} 条配方文件")

            return True

        except Exception as e:
            print(f"❌ 数据获取测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("开始测试修复后的手动排产功能...")
    
    # 测试数据获取
    data_test_result = test_data_retrieval()
    
    # 测试排产功能
    scheduling_test_result = test_manual_scheduling()
    
    print("\n=== 测试总结 ===")
    print(f"数据获取测试: {'✅ 通过' if data_test_result else '❌ 失败'}")
    print(f"排产功能测试: {'✅ 通过' if scheduling_test_result else '❌ 失败'}")
    
    if data_test_result and scheduling_test_result:
        print("🎉 所有测试通过！手动排产功能修复成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
