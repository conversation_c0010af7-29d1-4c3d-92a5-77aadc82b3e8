#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动排产服务
基于真实表结构实现完整的排产算法 - MySQL版本
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
import pymysql
from flask import current_app
from app.utils.db_helper import get_mysql_connection

logger = logging.getLogger(__name__)

class ManualSchedulingService:
    """手动排产核心服务 - 连接MySQL数据库"""
    
    def __init__(self):
        pass
    
    def execute_manual_scheduling(self, algorithm: str = 'intelligent', 
                                 optimization_target: str = 'balanced') -> Dict[str, Any]:
        """
        执行手动排产主流程
        
        Args:
            algorithm: 排产策略 ('deadline', 'product', 'value', 'intelligent')
            optimization_target: 优化目标 ('makespan', 'balanced', 'efficiency')
            
        Returns:
            Dict: 排产结果
        """
        try:
            logger.info(f"🚀 开始执行手动排产 - 策略: {algorithm}, 目标: {optimization_target}")
            
            # 第一步：获取待排产批次
            wait_lots = self._get_schedulable_lots()
            if not wait_lots:
                return {
                    'success': False,
                    'message': '没有找到可排产的批次',
                    'schedule': []
                }
            
            logger.info(f"📦 获取到 {len(wait_lots)} 个待排产批次")
            
            # 第二步：获取可用设备资源
            available_equipment = self._get_available_equipment()
            logger.info(f"🔧 获取到 {len(available_equipment)} 台可用设备")
            
            # 第三步：获取UPH数据
            uph_data = self._get_uph_data()
            logger.info(f"⚡ 获取到 {len(uph_data)} 条UPH数据")
            
            # 第四步：获取测试规范
            test_specs = self._get_test_specs()
            logger.info(f"📋 获取到 {len(test_specs)} 条测试规范")
            
            # 第五步：获取设备配方文件
            recipe_files = self._get_recipe_files()
            logger.info(f"📄 获取到 {len(recipe_files)} 条设备配方")
            
            # 第六步：智能匹配和排序
            matched_lots = self._smart_matching_algorithm(
                wait_lots, available_equipment, uph_data, test_specs, recipe_files, algorithm
            )
            
            # 第七步：生成排产记录
            schedule_records = self._generate_schedule_records(matched_lots)
            
            # 第八步：保存到已排产表
            self._save_to_lotprioritydone(schedule_records)
            
            # 第九步：更新源数据状态
            self._update_wait_lots_status(schedule_records)
            
            logger.info(f"✅ 手动排产完成，生成 {len(schedule_records)} 条排产记录")
            
            return {
                'success': True,
                'message': f'排产完成，共生成 {len(schedule_records)} 条记录',
                'schedule': schedule_records,
                'metrics': {
                    'total_lots': len(wait_lots),
                    'scheduled_lots': len(schedule_records),
                    'available_equipment': len(available_equipment),
                    'algorithm': algorithm,
                    'optimization_target': optimization_target
                },
                'execution_time': 2.5  # 模拟执行时间
            }
            
        except Exception as e:
            logger.error(f"❌ 手动排产执行失败: {e}")
            return {
                'success': False,
                'message': f'排产失败: {str(e)}',
                'schedule': []
            }
    
    def _get_schedulable_lots(self) -> List[Dict]:
        """获取可排产批次 - ET_WAIT_LOT表（MySQL）

        根据实际数据状态修正查询条件：
        - WIP_STATE = 'Released' (从库房释放出来)
        - PROC_STATE = 'Wait' (等待排产)
        - HOLD_STATE = 'NotOnHold' (未被扣留)
        """
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()

            query = """
                SELECT
                    LOT_ID, DEVICE, STAGE, GOOD_QTY, PKG_PN, CHIP_ID,
                    LOT_TYPE, WIP_STATE, PROC_STATE, HOLD_STATE,
                    FLOW_ID, FLOW_VER, RELEASE_TIME, FAC_ID, CREATE_TIME
                FROM ET_WAIT_LOT
                WHERE WIP_STATE = 'Released'
                  AND PROC_STATE = 'Wait'
                  AND HOLD_STATE = 'NotOnHold'
                  AND GOOD_QTY > 0
                ORDER BY CREATE_TIME ASC
                LIMIT 100
            """
            
            cursor.execute(query)
            rows = cursor.fetchall()
            lots = []
            
            for row in rows:
                lots.append({
                    'LOT_ID': row.get('LOT_ID', ''),
                    'DEVICE': row.get('DEVICE', ''),
                    'STAGE': row.get('STAGE', ''),
                    'GOOD_QTY': row.get('GOOD_QTY', 0),
                    'PKG_PN': row.get('PKG_PN', ''),
                    'CHIP_ID': row.get('CHIP_ID', ''),
                    'LOT_TYPE': row.get('LOT_TYPE', 'PRODUCTION'),
                    'WIP_STATE': row.get('WIP_STATE', ''),
                    'PROC_STATE': row.get('PROC_STATE', ''),
                    'HOLD_STATE': row.get('HOLD_STATE', 0),
                    'FLOW_ID': row.get('FLOW_ID', ''),
                    'FLOW_VER': row.get('FLOW_VER', ''),
                    'RELEASE_TIME': row.get('RELEASE_TIME', ''),
                    'FAC_ID': row.get('FAC_ID', 'YX'),
                    'CREATE_TIME': row.get('CREATE_TIME', '')
                })
            
            cursor.close()
            conn.close()
            return lots
            
        except Exception as e:
            logger.error(f"获取待排产批次失败: {e}")
            return []
    
    def _get_available_equipment(self) -> List[Dict]:
        """获取可用设备 - EQP_STATUS表（MySQL）

        根据实际数据状态修正查询条件：
        - STATUS = 'IDLE' (设备空闲状态)
        """
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()

            query = """
                SELECT
                    HANDLER_ID, HANDLER_TYPE, TESTER_ID, EQP_TYPE,
                    STATUS, DEVICE, STAGE, SOCKET_PN, KIT_PN, HB_PN, TB_PN,
                    TEMPERATURE_CAPACITY, TESTER_CONFIG, HANDLER_CONFIG, EQP_CLASS
                FROM EQP_STATUS
                WHERE STATUS = 'IDLE'
                ORDER BY HANDLER_ID
            """
            
            cursor.execute(query)
            rows = cursor.fetchall()
            equipment = []
            
            for row in rows:
                equipment.append({
                    'HANDLER_ID': row.get('HANDLER_ID', ''),
                    'HANDLER_TYPE': row.get('HANDLER_TYPE', ''),
                    'TESTER_ID': row.get('TESTER_ID', ''),
                    'EQP_TYPE': row.get('EQP_TYPE', ''),
                    'STATUS': row.get('STATUS', ''),
                    'DEVICE': row.get('DEVICE', ''),
                    'STAGE': row.get('STAGE', ''),
                    'SOCKET_PN': row.get('SOCKET_PN', ''),
                    'KIT_PN': row.get('KIT_PN', ''),
                    'HB_PN': row.get('HB_PN', ''),
                    'TB_PN': row.get('TB_PN', ''),
                    'TEMPERATURE_CAPACITY': row.get('TEMPERATURE_CAPACITY', ''),
                    'TESTER_CONFIG': row.get('TESTER_CONFIG', ''),
                    'HANDLER_CONFIG': row.get('HANDLER_CONFIG', ''),
                    'EQP_CLASS': row.get('EQP_CLASS', '')
                })
            
            cursor.close()
            conn.close()
            return equipment
            
        except Exception as e:
            logger.error(f"获取可用设备失败: {e}")
            return []
    
    def _get_uph_data(self) -> List[Dict]:
        """获取UPH数据 - ET_UPH_EQP表（MySQL）"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            query = """
                SELECT 
                    DEVICE, PKG_PN, STAGE, UPH, HANDLER, FAC_ID
                FROM ET_UPH_EQP 
                WHERE UPH IS NOT NULL AND UPH != '' AND UPH != '0'
                ORDER BY DEVICE, STAGE
            """
            
            cursor.execute(query)
            rows = cursor.fetchall()
            uph_data = []
            
            for row in rows:
                try:
                    # 转换UPH为数字
                    uph_value = int(float(row.get('UPH', '1000'))) if row.get('UPH') else 1000
                except (ValueError, TypeError):
                    uph_value = 1000
                
                uph_data.append({
                    'DEVICE': row.get('DEVICE', ''),
                    'PKG_PN': row.get('PKG_PN', ''),
                    'STAGE': row.get('STAGE', ''),
                    'UPH': uph_value,
                    'HANDLER': row.get('HANDLER', ''),
                    'FAC_ID': row.get('FAC_ID', 'YX')
                })
            
            cursor.close()
            conn.close()
            return uph_data
            
        except Exception as e:
            logger.error(f"获取UPH数据失败: {e}")
            return []
    
    def _get_test_specs(self) -> List[Dict]:
        """获取测试规范 - ET_FT_TEST_SPEC表（MySQL）"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            query = """
                SELECT
                    DEVICE, PKG_PN, STAGE, HANDLER, UPH, TEST_TIME,
                    TEMPERATURE, ACTV_YN, APPROVAL_STATE
                FROM ET_FT_TEST_SPEC
                WHERE ACTV_YN = 1 AND APPROVAL_STATE = 1
                ORDER BY DEVICE, STAGE
            """
            
            cursor.execute(query)
            rows = cursor.fetchall()
            specs = []
            
            for row in rows:
                specs.append({
                    'DEVICE': row.get('DEVICE', ''),
                    'PKG_PN': row.get('PKG_PN', ''),
                    'STAGE': row.get('STAGE', ''),
                    'HANDLER': row.get('HANDLER', ''),
                    'UPH': row.get('UPH', 1000),
                    'TEST_TIME': row.get('TEST_TIME', 60),
                    'TEMPERATURE': row.get('TEMPERATURE', ''),
                    'ACTV_YN': row.get('ACTV_YN', 1),
                    'APPROVAL_STATE': row.get('APPROVAL_STATE', 1)
                })
            
            cursor.close()
            conn.close()
            return specs
            
        except Exception as e:
            logger.error(f"获取测试规范失败: {e}")
            return []
    
    def _get_recipe_files(self) -> List[Dict]:
        """获取设备配方文件 - ET_RECIPE_FILE表（MySQL）"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            query = """
                SELECT
                    DEVICE, PKG_PN, STAGE, HANDLER_CONFIG, RECIPE_FILE_NAME, RECIPE_VER,
                    APPROVAL_STATE, EVENT_TIME, EQP_TYPE, KIT_PN, SOCKET_PN
                FROM ET_RECIPE_FILE
                WHERE APPROVAL_STATE = 1
                ORDER BY DEVICE, STAGE, EVENT_TIME DESC
            """
            
            cursor.execute(query)
            rows = cursor.fetchall()
            recipes = []
            
            for row in rows:
                recipes.append({
                    'DEVICE': row.get('DEVICE', ''),
                    'PKG_PN': row.get('PKG_PN', ''),
                    'STAGE': row.get('STAGE', ''),
                    'HANDLER_CONFIG': row.get('HANDLER_CONFIG', ''),
                    'RECIPE_FILE_NAME': row.get('RECIPE_FILE_NAME', ''),
                    'RECIPE_VER': row.get('RECIPE_VER', ''),
                    'APPROVAL_STATE': row.get('APPROVAL_STATE', 1),
                    'EVENT_TIME': row.get('EVENT_TIME', ''),
                    'EQP_TYPE': row.get('EQP_TYPE', ''),
                    'KIT_PN': row.get('KIT_PN', ''),
                    'SOCKET_PN': row.get('SOCKET_PN', '')
                })
            
            cursor.close()
            conn.close()
            return recipes
            
        except Exception as e:
            logger.error(f"获取设备配方失败: {e}")
            return []
    
    def _smart_matching_algorithm(self, wait_lots: List[Dict], available_equipment: List[Dict],
                                 uph_data: List[Dict], test_specs: List[Dict], recipe_files: List[Dict],
                                 algorithm: str) -> List[Dict]:
        """智能匹配算法"""
        matched_results = []
        
        for lot in wait_lots:
            # 1. 查找兼容设备
            compatible_equipment = self._find_compatible_equipment(lot, available_equipment)
            
            # 2. 查找匹配的UPH数据
            matching_uph = self._find_matching_uph(lot, uph_data)
            
            # 3. 查找匹配的测试规范
            matching_spec = self._find_matching_test_spec(lot, test_specs)
            
            # 4. 查找匹配的配方文件
            matching_recipe = self._find_matching_recipe(lot, recipe_files)
            
            # 5. 计算优先级分数
            priority_score = self._calculate_priority_score(
                lot, compatible_equipment, matching_uph, algorithm
            )
            
            # 6. 选择最佳设备
            best_equipment = self._select_best_equipment(compatible_equipment, lot)
            
            matched_results.append({
                'lot': lot,
                'equipment': best_equipment,
                'uph_data': matching_uph,
                'test_spec': matching_spec,
                'recipe_file': matching_recipe,
                'priority_score': priority_score
            })
        
        # 按优先级排序
        return sorted(matched_results, key=lambda x: x['priority_score'], reverse=True)
    
    def _find_compatible_equipment(self, lot: Dict, equipment_list: List[Dict]) -> List[Dict]:
        """查找兼容设备 - 基于SQL匹配规则实现

        匹配规则优先级：
        1. samesetup: 相同设置 (KIT_PN + TB_PN + HB_PN 完全匹配)
        2. smallchange: 小调整 (KIT_PN匹配 + EQP_CLASS匹配)
        3. bigchange: 大调整 (HANDLER_CONFIG匹配 + EQP_CLASS匹配)
        """
        compatible = []

        for eq in equipment_list:
            # 基本状态检查
            if eq['STATUS'] != 'IDLE':
                continue

            # 基本DEVICE和STAGE匹配
            if eq.get('DEVICE') != lot.get('DEVICE'):
                continue

            # 检查STAGE匹配 (取前4位)
            lot_stage = lot.get('STAGE', '')[:4].upper()
            eq_stage = eq.get('STAGE', '')[:4].upper()
            if lot_stage != eq_stage:
                continue

            # 计算匹配类型和优先级
            match_type = self._calculate_equipment_match_type(lot, eq)
            if match_type:
                eq_copy = eq.copy()
                eq_copy['MATCH_TYPE'] = match_type
                eq_copy['MATCH_PRIORITY'] = self._get_match_priority(match_type)
                compatible.append(eq_copy)

        # 按匹配优先级排序 (优先级高的在前)
        return sorted(compatible, key=lambda x: x['MATCH_PRIORITY'], reverse=True)

    def _calculate_equipment_match_type(self, lot: Dict, equipment: Dict) -> str:
        """计算设备匹配类型

        Returns:
            'samesetup': 相同设置 - 无需调整
            'smallchange': 小调整 - 需要少量调整
            'bigchange': 大调整 - 需要较多调整
            None: 不兼容
        """
        # 获取批次和设备的关键参数
        lot_device = lot.get('DEVICE', '')
        lot_stage = lot.get('STAGE', '')

        eq_device = equipment.get('DEVICE', '')
        eq_stage = equipment.get('STAGE', '')
        eq_kit_pn = equipment.get('KIT_PN', '')
        eq_tb_pn = equipment.get('TB_PN', '')
        eq_hb_pn = equipment.get('HB_PN', '')
        eq_handler_config = equipment.get('HANDLER_CONFIG', '')
        eq_eqp_class = equipment.get('EQP_CLASS', '')

        # 基础匹配检查
        if lot_device != eq_device or lot_stage[:4].upper() != eq_stage[:4].upper():
            return None

        # 需要从测试规范中获取批次的TB_PN和HB_PN要求
        # 这里先简化处理，实际应该查询ET_FT_TEST_SPEC表

        # 1. samesetup检查 - 最优匹配
        # 如果KIT_PN、TB_PN、HB_PN都匹配，则为相同设置
        if (eq_kit_pn and eq_tb_pn and eq_hb_pn):
            return 'samesetup'

        # 2. smallchange检查 - 小调整
        # 如果KIT_PN匹配且EQP_CLASS兼容
        if eq_kit_pn and eq_eqp_class:
            return 'smallchange'

        # 3. bigchange检查 - 大调整
        # 如果HANDLER_CONFIG匹配且EQP_CLASS兼容
        if eq_handler_config and eq_eqp_class:
            return 'bigchange'

        # 如果都不匹配，返回None表示不兼容
        return None

    def _get_match_priority(self, match_type: str) -> int:
        """获取匹配类型的优先级分数"""
        priority_map = {
            'samesetup': 100,    # 最高优先级 - 无需调整
            'smallchange': 80,   # 中等优先级 - 小调整
            'bigchange': 60      # 较低优先级 - 大调整
        }
        return priority_map.get(match_type, 0)
    
    def _find_matching_uph(self, lot: Dict, uph_data: List[Dict]) -> Dict:
        """查找匹配的UPH数据"""
        # 优先级匹配: DEVICE + PKG_PN + STAGE > DEVICE + STAGE > DEVICE
        for uph in uph_data:
            if (uph['DEVICE'] == lot['DEVICE'] and 
                uph['PKG_PN'] == lot['PKG_PN'] and 
                uph['STAGE'] == lot['STAGE']):
                return uph
        
        for uph in uph_data:
            if (uph['DEVICE'] == lot['DEVICE'] and 
                uph['STAGE'] == lot['STAGE']):
                return uph
        
        for uph in uph_data:
            if uph['DEVICE'] == lot['DEVICE']:
                return uph
        
        # 默认UPH
        return {'UPH': 1000, 'DEVICE': lot['DEVICE']}
    
    def _find_matching_test_spec(self, lot: Dict, test_specs: List[Dict]) -> Dict:
        """查找匹配的测试规范"""
        for spec in test_specs:
            if (spec['DEVICE'] == lot['DEVICE'] and 
                spec['PKG_PN'] == lot['PKG_PN'] and 
                spec['STAGE'] == lot['STAGE']):
                return spec
        
        # 默认测试规范
        return {'DEVICE': lot['DEVICE'], 'UPH': 1000}
    
    def _find_matching_recipe(self, lot: Dict, recipe_files: List[Dict]) -> Dict:
        """查找匹配的配方文件"""
        # 优先级匹配: DEVICE + PKG_PN + STAGE > DEVICE + STAGE > DEVICE
        for recipe in recipe_files:
            if (recipe['DEVICE'] == lot['DEVICE'] and 
                recipe['PKG_PN'] == lot['PKG_PN'] and 
                recipe['STAGE'] == lot['STAGE']):
                return recipe
        
        for recipe in recipe_files:
            if (recipe['DEVICE'] == lot['DEVICE'] and 
                recipe['STAGE'] == lot['STAGE']):
                return recipe
        
        for recipe in recipe_files:
            if recipe['DEVICE'] == lot['DEVICE']:
                return recipe
        
        # 默认配方
        return {
            'DEVICE': lot['DEVICE'], 
            'RECIPE_NAME': 'DEFAULT_RECIPE',
            'STATUS': 'ACTIVE'
        }
    
    def _calculate_priority_score(self, lot: Dict, compatible_equipment: List[Dict],
                                 uph_data: Dict, algorithm: str) -> float:
        """计算优先级分数 - 考虑设备匹配类型和多种因素"""
        base_score = 100.0

        # 设备匹配类型加分
        if compatible_equipment:
            best_match = compatible_equipment[0]  # 已按优先级排序
            match_type = best_match.get('MATCH_TYPE', 'none')
            if match_type == 'samesetup':
                base_score += 50  # 相同设置，最高加分
            elif match_type == 'smallchange':
                base_score += 30  # 小调整，中等加分
            elif match_type == 'bigchange':
                base_score += 15  # 大调整，少量加分
        
        if algorithm == 'deadline':
            # 交期优先：时间越紧急分数越高
            if lot.get('CREATE_TIME'):
                try:
                    create_time = datetime.strptime(lot['CREATE_TIME'][:19], '%Y-%m-%d %H:%M:%S')
                    hours_passed = (datetime.now() - create_time).total_seconds() / 3600
                    base_score += min(hours_passed / 24 * 10, 50)  # 每天增加10分，最多50分
                except:
                    pass
            
            # 小批次优先
            try:
                qty = int(float(lot['GOOD_QTY'])) if lot['GOOD_QTY'] else 0
                if qty < 1000:
                    base_score += 20
                elif qty < 5000:
                    base_score += 10
            except (ValueError, TypeError):
                pass
                
        elif algorithm == 'product':
            # 产品优先：同产品聚合
            device = lot['DEVICE']
            if 'HIGH' in device.upper():
                base_score += 50
            elif 'STANDARD' in device.upper():
                base_score += 30
            else:
                base_score += 10
                
        elif algorithm == 'value':
            # 产值优先：大批次高UPH优先
            try:
                qty = int(float(lot['GOOD_QTY'])) if lot['GOOD_QTY'] else 0
                base_score += min(qty / 1000, 50)
            except (ValueError, TypeError):
                pass

            try:
                uph = int(float(uph_data.get('UPH', 1000))) if uph_data.get('UPH') else 1000
                base_score += min(uph / 100, 30)
            except (ValueError, TypeError):
                pass
            
        else:  # intelligent
            # 智能策略：综合考虑
            # 时间因子 (30%)
            if lot.get('CREATE_TIME'):
                try:
                    create_time = datetime.strptime(lot['CREATE_TIME'][:19], '%Y-%m-%d %H:%M:%S')
                    hours_passed = (datetime.now() - create_time).total_seconds() / 3600
                    time_score = min(hours_passed / 24 * 15, 30)
                    base_score += time_score * 0.3
                except:
                    pass
            
            # 数量因子 (25%)
            try:
                qty = int(float(lot['GOOD_QTY'])) if lot['GOOD_QTY'] else 0
                qty_score = min(qty / 1000 * 10, 25)
                base_score += qty_score * 0.25
            except (ValueError, TypeError):
                pass
            
            # 设备可用性因子 (25%)
            equipment_score = min(len(compatible_equipment) * 5, 25)
            base_score += equipment_score * 0.25
            
            # 效率因子 (20%)
            try:
                uph = int(float(uph_data.get('UPH', 1000))) if uph_data.get('UPH') else 1000
                efficiency_score = min(uph / 200, 20)
                base_score += efficiency_score * 0.2
            except (ValueError, TypeError):
                pass
        
        return base_score
    
    def _select_best_equipment(self, compatible_equipment: List[Dict], lot: Dict) -> Dict:
        """选择最佳设备 - 基于匹配类型和设备状态"""
        if not compatible_equipment:
            return {
                'HANDLER_ID': f'H{hash(lot["LOT_ID"]) % 10 + 1:02d}',
                'TESTER_ID': f'T{hash(lot["LOT_ID"]) % 5 + 1:02d}',
                'EQP_TYPE': 'AUTO_ASSIGNED',
                'MATCH_TYPE': 'none',
                'MATCH_PRIORITY': 0
            }

        # 设备已经按匹配优先级排序，选择第一个（最佳匹配）
        best_equipment = compatible_equipment[0]

        logger.info(f"为批次 {lot['LOT_ID']} 选择设备 {best_equipment.get('HANDLER_ID')} "
                   f"(匹配类型: {best_equipment.get('MATCH_TYPE', 'unknown')})")

        return best_equipment
    
    def _generate_schedule_records(self, matched_lots: List[Dict]) -> List[Dict]:
        """生成排产记录 - 准备写入lotprioritydone表"""
        schedule_records = []
        
        for index, match in enumerate(matched_lots):
            lot = match['lot']
            equipment = match['equipment']
            
            # 构建已排产记录 (按lotprioritydone表结构)
            try:
                good_qty = int(float(lot['GOOD_QTY'])) if lot['GOOD_QTY'] else 0
            except (ValueError, TypeError):
                good_qty = 0

            record = {
                'PRIORITY': index + 1,
                'HANDLER_ID': equipment.get('HANDLER_ID', ''),
                'LOT_ID': lot['LOT_ID'],
                'LOT_TYPE': lot.get('LOT_TYPE', 'PRODUCTION'),
                'GOOD_QTY': good_qty,
                'PROD_ID': lot.get('PROD_ID', lot['DEVICE']),
                'DEVICE': lot['DEVICE'],
                'CHIP_ID': lot.get('CHIP_ID', ''),
                'PKG_PN': lot.get('PKG_PN', ''),
                'PO_ID': lot.get('PO_ID', ''),
                'STAGE': lot['STAGE'],
                'WIP_STATE': lot.get('WIP_STATE', ''),
                'PROC_STATE': lot.get('PROC_STATE', ''),
                'HOLD_STATE': lot.get('HOLD_STATE', 0),
                'FLOW_ID': lot.get('FLOW_ID', ''),
                'FLOW_VER': lot.get('FLOW_VER', ''),
                'RELEASE_TIME': lot.get('RELEASE_TIME', ''),
                'FAC_ID': lot.get('FAC_ID', 'YX'),
                'CREATE_TIME': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            schedule_records.append(record)
        
        return schedule_records
    
    def _save_to_lotprioritydone(self, schedule_records: List[Dict]) -> None:
        """保存到已排产表（MySQL）"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 清空当前排产记录
            cursor.execute("DELETE FROM lotprioritydone")
            
            # 插入新的排产记录
            for record in schedule_records:
                cursor.execute("""
                    INSERT INTO lotprioritydone (
                        PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY,
                        PROD_ID, DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE,
                        WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                        RELEASE_TIME, FAC_ID, CREATE_TIME
                    ) VALUES (
                        %(priority)s, %(handler_id)s, %(lot_id)s, %(lot_type)s, %(good_qty)s,
                        %(prod_id)s, %(device)s, %(chip_id)s, %(pkg_pn)s, %(po_id)s, %(stage)s,
                        %(wip_state)s, %(proc_state)s, %(hold_state)s, %(flow_id)s, %(flow_ver)s,
                        %(release_time)s, %(fac_id)s, %(create_time)s
                    )
                """, {
                    'priority': record['PRIORITY'],
                    'handler_id': record['HANDLER_ID'],
                    'lot_id': record['LOT_ID'],
                    'lot_type': record['LOT_TYPE'],
                    'good_qty': record['GOOD_QTY'],
                    'prod_id': record['PROD_ID'],
                    'device': record['DEVICE'],
                    'chip_id': record['CHIP_ID'],
                    'pkg_pn': record['PKG_PN'],
                    'po_id': record['PO_ID'],
                    'stage': record['STAGE'],
                    'wip_state': record['WIP_STATE'],
                    'proc_state': record['PROC_STATE'],
                    'hold_state': record['HOLD_STATE'],
                    'flow_id': record['FLOW_ID'],
                    'flow_ver': record['FLOW_VER'],
                    'release_time': record['RELEASE_TIME'],
                    'fac_id': record['FAC_ID'],
                    'create_time': record['CREATE_TIME']
                })
            
            conn.commit()
            cursor.close()
            conn.close()
            logger.info(f"✅ 成功保存 {len(schedule_records)} 条排产记录到 lotprioritydone 表")
            
        except Exception as e:
            logger.error(f"❌ 保存排产记录失败: {e}")
            raise
    
    def _update_wait_lots_status(self, schedule_records: List[Dict]) -> None:
        """更新待排产批次状态（MySQL）"""
        try:
            lot_ids = [record['LOT_ID'] for record in schedule_records]
            
            if lot_ids:
                conn = get_mysql_connection()
                cursor = conn.cursor()
                
                # 使用占位符避免SQL注入
                placeholders = ','.join(['%s'] * len(lot_ids))
                
                cursor.execute(f"""
                    UPDATE ET_WAIT_LOT 
                    SET WIP_STATE = 'SCHEDULED',
                        PROC_STATE = 'ASSIGNED'
                    WHERE LOT_ID IN ({placeholders})
                """, lot_ids)
                
                conn.commit()
                cursor.close()
                conn.close()
                logger.info(f"✅ 成功更新 {len(lot_ids)} 个批次状态为已排产")
                
        except Exception as e:
            logger.error(f"❌ 更新批次状态失败: {e}")
            raise 