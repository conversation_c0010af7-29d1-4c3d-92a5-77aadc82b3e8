-- =====================================================
-- 创新排产算法数据库扩展
-- 为数字孪生和AI算法提供数据支撑
-- =====================================================

-- 1. 设备数字孪生状态表
CREATE TABLE IF NOT EXISTS equipment_digital_twin (
    id INT AUTO_INCREMENT PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 实时状态数据
    temperature DECIMAL(5,2),
    humidity DECIMAL(5,2),
    vibration_level DECIMAL(8,4),
    power_consumption DECIMAL(10,2),
    cycle_count INT DEFAULT 0,
    
    -- 健康状态评估
    health_score DECIMAL(3,2) DEFAULT 1.00,  -- 0-1之间
    predicted_failure_time TIMESTAMP NULL,
    maintenance_urgency ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'LOW',
    
    -- 能力评估
    current_efficiency DECIMAL(3,2) DEFAULT 1.00,
    estimated_uph INT,
    quality_index DECIMAL(3,2) DEFAULT 1.00,
    
    -- 状态标识
    is_active BOOLEAN DEFAULT TRUE,
    last_maintenance TIMESTAMP NULL,
    
    INDEX idx_equipment_time (equipment_id, timestamp),
    INDEX idx_health_score (health_score),
    FOREIGN KEY (equipment_id) REFERENCES eqp_status(HANDLER_ID) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备数字孪生实时状态表';

-- 2. 设备切换历史记录表
CREATE TABLE IF NOT EXISTS equipment_switch_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    switch_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 切换信息
    from_lot_id VARCHAR(50),
    to_lot_id VARCHAR(50),
    from_product VARCHAR(100),
    to_product VARCHAR(100),
    from_stage VARCHAR(50),
    to_stage VARCHAR(50),
    
    -- 切换类型和成本
    switch_type ENUM('samesetup', 'smallchange', 'bigchange', 'major_change') NOT NULL,
    predicted_switch_time INT,  -- 预测切换时间(分钟)
    actual_switch_time INT,     -- 实际切换时间(分钟)
    
    -- 温度变化
    from_temperature DECIMAL(5,2),
    to_temperature DECIMAL(5,2),
    temperature_change_time INT, -- 温度变化时间(分钟)
    
    -- 工具和配置变化
    tool_change_required BOOLEAN DEFAULT FALSE,
    calibration_required BOOLEAN DEFAULT FALSE,
    recipe_change_required BOOLEAN DEFAULT FALSE,
    
    -- 切换效果
    first_pass_yield DECIMAL(5,2), -- 切换后首批良率
    ramp_up_time INT,              -- 爬坡时间(分钟)
    
    INDEX idx_equipment_switch (equipment_id, switch_timestamp),
    INDEX idx_switch_type (switch_type),
    INDEX idx_products (from_product, to_product)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备切换历史记录表';

-- 3. 排产决策历史表
CREATE TABLE IF NOT EXISTS scheduling_decision_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    decision_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 决策上下文
    algorithm_version VARCHAR(50),
    decision_type ENUM('initial', 'reschedule', 'emergency') DEFAULT 'initial',
    trigger_event VARCHAR(200),
    
    -- 输入状态
    total_lots INT,
    available_equipment INT,
    system_load_factor DECIMAL(3,2),
    
    -- 决策结果
    scheduled_lots INT,
    average_priority_score DECIMAL(8,4),
    estimated_makespan INT, -- 预计完成时间(小时)
    estimated_efficiency DECIMAL(3,2),
    
    -- 实际执行结果
    actual_makespan INT NULL,
    actual_efficiency DECIMAL(3,2) NULL,
    actual_yield DECIMAL(5,2) NULL,
    
    -- 学习反馈
    decision_quality_score DECIMAL(3,2) NULL, -- 决策质量评分
    lessons_learned TEXT,
    
    INDEX idx_decision_time (decision_timestamp),
    INDEX idx_algorithm (algorithm_version),
    INDEX idx_quality (decision_quality_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排产决策历史表';

-- 4. 产品工艺特征表
CREATE TABLE IF NOT EXISTS product_process_features (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_name VARCHAR(100) NOT NULL,
    stage VARCHAR(50) NOT NULL,
    
    -- 工艺特征
    standard_temperature DECIMAL(5,2),
    temperature_tolerance DECIMAL(3,2),
    standard_test_time INT, -- 标准测试时间(秒)
    complexity_level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM',
    
    -- 质量特征
    target_yield DECIMAL(5,2),
    yield_sensitivity DECIMAL(3,2), -- 对环境变化的敏感度
    critical_parameters TEXT, -- JSON格式存储关键参数
    
    -- 调度特征
    setup_complexity ENUM('SIMPLE', 'MODERATE', 'COMPLEX', 'VERY_COMPLEX') DEFAULT 'MODERATE',
    preferred_equipment TEXT, -- JSON格式存储首选设备列表
    incompatible_equipment TEXT, -- JSON格式存储不兼容设备
    
    -- 时间特征
    min_batch_size INT DEFAULT 1,
    max_batch_size INT DEFAULT 999999,
    processing_priority ENUM('LOW', 'NORMAL', 'HIGH', 'URGENT') DEFAULT 'NORMAL',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_product_stage (product_name, stage),
    INDEX idx_complexity (complexity_level),
    INDEX idx_priority (processing_priority)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品工艺特征表';

-- 5. AI模型参数表
CREATE TABLE IF NOT EXISTS ai_model_parameters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    model_type ENUM('switch_cost', 'yield_prediction', 'health_assessment', 'scheduling_rl') NOT NULL,
    
    -- 模型参数
    parameters JSON,
    hyperparameters JSON,
    
    -- 训练信息
    training_data_size INT,
    training_accuracy DECIMAL(5,4),
    validation_accuracy DECIMAL(5,4),
    last_training_time TIMESTAMP,
    
    -- 部署信息
    is_active BOOLEAN DEFAULT FALSE,
    deployment_time TIMESTAMP NULL,
    performance_metrics JSON,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_model_version (model_name, model_version),
    INDEX idx_model_type (model_type),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI模型参数表';

-- 6. 实时事件日志表
CREATE TABLE IF NOT EXISTS real_time_events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_timestamp TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3),
    
    -- 事件信息
    event_type ENUM('equipment_status', 'lot_status', 'quality_alert', 'schedule_change', 'system_alert') NOT NULL,
    event_source VARCHAR(100), -- 事件来源
    event_severity ENUM('INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO',
    
    -- 事件内容
    equipment_id VARCHAR(50),
    lot_id VARCHAR(50),
    event_data JSON,
    event_message TEXT,
    
    -- 处理状态
    is_processed BOOLEAN DEFAULT FALSE,
    processed_by VARCHAR(100),
    processed_at TIMESTAMP NULL,
    processing_result TEXT,
    
    INDEX idx_event_time (event_timestamp),
    INDEX idx_event_type (event_type),
    INDEX idx_equipment (equipment_id),
    INDEX idx_processed (is_processed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时事件日志表';

-- 插入初始化数据
INSERT INTO product_process_features (product_name, stage, standard_temperature, temperature_tolerance, standard_test_time, complexity_level, target_yield, setup_complexity) VALUES
('JWQ87820ESOP-SJA1_TR1', 'HOT-FT', 125.0, 5.0, 180, 'MEDIUM', 98.5, 'MODERATE'),
('JWQ85213-C244QFNA-SJA1_TR1', 'HOT-FT', 125.0, 5.0, 200, 'HIGH', 97.8, 'COMPLEX'),
('JWQ52992UDHBA_TR1', 'HOT-FT', 125.0, 5.0, 160, 'MEDIUM', 98.2, 'MODERATE'),
('JWQ5217DFND-M001_TR1', 'HOT-FT', 125.0, 5.0, 190, 'HIGH', 97.5, 'COMPLEX'),
('JWQ7101SOTB-J115_TR1', 'HOT-FT', 125.0, 5.0, 170, 'MEDIUM', 98.0, 'MODERATE'),
('JWH7030QFNAZ', 'BAKING2', 150.0, 10.0, 300, 'LOW', 99.2, 'SIMPLE'),
('JWQ950133-50ESOP_TR1', 'BAKING2', 150.0, 10.0, 280, 'LOW', 99.0, 'SIMPLE');

-- 创建视图：设备实时状态汇总
CREATE OR REPLACE VIEW v_equipment_real_time_status AS
SELECT 
    e.HANDLER_ID,
    e.STATUS as current_status,
    e.DEVICE,
    e.STAGE,
    dt.health_score,
    dt.current_efficiency,
    dt.estimated_uph,
    dt.temperature,
    dt.maintenance_urgency,
    dt.timestamp as last_update,
    CASE 
        WHEN dt.health_score >= 0.9 AND e.STATUS = 'IDLE' THEN 'EXCELLENT'
        WHEN dt.health_score >= 0.8 AND e.STATUS = 'IDLE' THEN 'GOOD'
        WHEN dt.health_score >= 0.7 THEN 'FAIR'
        WHEN dt.health_score >= 0.5 THEN 'POOR'
        ELSE 'CRITICAL'
    END as overall_condition
FROM eqp_status e
LEFT JOIN equipment_digital_twin dt ON e.HANDLER_ID = dt.equipment_id
WHERE dt.is_active = TRUE 
   OR dt.equipment_id IS NULL;
