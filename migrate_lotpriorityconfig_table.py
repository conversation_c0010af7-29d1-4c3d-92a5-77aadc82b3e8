#!/usr/bin/env python3
"""
批次优先级配置表数据库迁移脚本
重建表结构以添加lot_id字段并匹配Excel文件的完整字段
"""

import mysql.connector
import sys
import os
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'user': 'root',
    'password': 'WWWwww123!',
    'database': 'aps_system',
    'charset': 'utf8mb4'
}

def connect_db():
    """连接数据库"""
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def backup_existing_table(conn):
    """备份现有表"""
    try:
        cursor = conn.cursor()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 备份lotpriorityconfig表
        try:
            cursor.execute("SHOW TABLES LIKE 'lotpriorityconfig'")
            if cursor.fetchone():
                backup_table = f"lotpriorityconfig_backup_{timestamp}"
                cursor.execute(f"CREATE TABLE {backup_table} AS SELECT * FROM lotpriorityconfig")
                cursor.execute(f"SELECT COUNT(*) FROM {backup_table}")
                count = cursor.fetchone()[0]
                print(f"✅ 已备份 lotpriorityconfig 表到 {backup_table}，共 {count} 条记录")
                conn.commit()
            else:
                print("ℹ️  lotpriorityconfig 表不存在，无需备份")
        except Exception as e:
            print(f"⚠️  备份 lotpriorityconfig 表时出错: {e}")
        
        cursor.close()
        return True
    except Exception as e:
        print(f"❌ 备份表失败: {e}")
        return False

def recreate_lotpriorityconfig_table(conn):
    """重新创建lotpriorityconfig表，包含lot_id字段"""
    try:
        cursor = conn.cursor()
        
        # 删除现有表
        cursor.execute("DROP TABLE IF EXISTS lotpriorityconfig")
        print("✅ 已删除旧的 lotpriorityconfig 表")
        
        # 创建新的lotpriorityconfig表
        create_sql = """
        CREATE TABLE lotpriorityconfig (
            id INT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
            lot_id VARCHAR(100) NOT NULL COMMENT 'lot_id - 批次ID',
            device VARCHAR(200) NOT NULL COMMENT 'device - 产品名称',
            stage VARCHAR(100) DEFAULT NULL COMMENT 'stage - 工序',
            priority VARCHAR(10) DEFAULT NULL COMMENT 'priority - 优先级',
            refresh_time DATETIME DEFAULT NULL COMMENT 'refresh_time - 刷新时间',
            user VARCHAR(100) DEFAULT NULL COMMENT 'user - 用户',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_lot_id (lot_id),
            INDEX idx_device (device),
            INDEX idx_priority (priority),
            INDEX idx_stage (stage)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='批次优先级配置表'
        """
        
        cursor.execute(create_sql)
        print("✅ 已创建新的 lotpriorityconfig 表（包含lot_id字段）")
        
        conn.commit()
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建表失败: {e}")
        return False

def verify_table_structure(conn):
    """验证表结构"""
    try:
        cursor = conn.cursor()
        cursor.execute("DESCRIBE lotpriorityconfig")
        columns = cursor.fetchall()
        
        print("\n📋 lotpriorityconfig 表结构:")
        for col in columns:
            print(f"  - {col[0]}: {col[1]} {col[2] if col[2] == 'NO' else 'NULL'}")
        
        cursor.close()
        return True
    except Exception as e:
        print(f"❌ 验证表结构失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始批次优先级配置表迁移...")
    print(f"目标数据库: {DB_CONFIG['host']}:{DB_CONFIG['database']}")
    
    # 连接数据库
    conn = connect_db()
    if not conn:
        sys.exit(1)
    
    try:
        # 备份现有表
        if not backup_existing_table(conn):
            print("❌ 备份失败，终止迁移")
            sys.exit(1)
        
        # 重新创建表
        if not recreate_lotpriorityconfig_table(conn):
            print("❌ 表创建失败，终止迁移")
            sys.exit(1)
        
        # 验证表结构
        if not verify_table_structure(conn):
            print("❌ 表结构验证失败")
            sys.exit(1)
            
        print("\n🎉 批次优先级配置表迁移完成！")
        print("✅ 新表已创建，包含以下字段:")
        print("   - id (自增主键)")
        print("   - lot_id (批次ID) - 新增字段")
        print("   - device (产品名称)")
        print("   - stage (工序)")
        print("   - priority (优先级)")
        print("   - refresh_time (刷新时间)")
        print("   - user (用户)")
        print("   - created_at (创建时间)")
        print("   - updated_at (更新时间)")
        
    finally:
        conn.close()

if __name__ == "__main__":
    main() 