#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的排产功能测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 创建Flask应用上下文
from app import create_app
from app.services.manual_scheduling_service import ManualSchedulingService
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建应用实例
app_result = create_app()
if isinstance(app_result, tuple):
    app = app_result[0]  # 如果返回元组，取第一个元素
else:
    app = app_result

def test_scheduling():
    """测试排产功能"""
    with app.app_context():
        try:
            print("=== 测试手动排产功能 ===")
            
            # 创建排产服务实例
            service = ManualSchedulingService()
            
            # 测试数据获取
            print("\n1. 测试数据获取...")
            lots = service._get_schedulable_lots()
            print(f"   获取到 {len(lots)} 条待排产批次")
            
            equipment = service._get_available_equipment()
            print(f"   获取到 {len(equipment)} 台可用设备")
            
            if lots and equipment:
                print(f"   示例批次: {lots[0].get('LOT_ID')} - {lots[0].get('DEVICE')} - {lots[0].get('STAGE')}")
                print(f"   示例设备: {equipment[0].get('HANDLER_ID')} - {equipment[0].get('DEVICE')}")
            
            # 测试排产执行
            print("\n2. 测试排产执行...")
            result = service.execute_manual_scheduling(
                algorithm='intelligent',
                optimization_target='balanced'
            )
            
            print(f"排产结果: {result['success']}")
            print(f"消息: {result['message']}")
            
            if result['success']:
                schedule = result.get('schedule', [])
                metrics = result.get('metrics', {})
                
                print(f"生成排产记录数: {len(schedule)}")
                print(f"待排产批次总数: {metrics.get('total_lots', 0)}")
                print(f"成功排产批次数: {metrics.get('scheduled_lots', 0)}")
                print(f"可用设备数: {metrics.get('available_equipment', 0)}")
                
                # 显示前3条排产记录
                if schedule:
                    print("\n前3条排产记录:")
                    for i, record in enumerate(schedule[:3], 1):
                        lot = record.get('lot', {})
                        equipment = record.get('equipment', {})
                        print(f"  {i}. LOT_ID: {lot.get('LOT_ID', 'N/A')}")
                        print(f"     DEVICE: {lot.get('DEVICE', 'N/A')}")
                        print(f"     HANDLER_ID: {equipment.get('HANDLER_ID', 'N/A')}")
                        print(f"     MATCH_TYPE: {equipment.get('MATCH_TYPE', 'N/A')}")
                        print(f"     PRIORITY_SCORE: {record.get('priority_score', 0):.2f}")
                        print()
                
                return True
            else:
                print(f"排产失败: {result.get('message', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("开始测试修复后的手动排产功能...")
    
    success = test_scheduling()
    
    print("\n=== 测试总结 ===")
    if success:
        print("🎉 排产功能测试通过！")
    else:
        print("⚠️ 排产功能测试失败，需要进一步调试。")
